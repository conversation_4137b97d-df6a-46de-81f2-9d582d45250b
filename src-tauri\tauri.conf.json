{"$schema": "https://schema.tauri.app/config/2", "productName": "d-<PERSON><PERSON>", "version": "0.1.0", "identifier": "com.arekn.d-kasir", "build": {"beforeDevCommand": "bun run dev", "devUrl": "http://localhost:1422", "beforeBuildCommand": "bun run build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "d-<PERSON><PERSON>", "width": 800, "height": 600}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}