mod database;
mod models;
mod handlers;
mod utils;

use database::create_database_connection;
use handlers::*;
use tauri::Manager;

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    // Initialize logging
    tracing_subscriber::fmt::init();

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .setup(|app| {
            // Initialize database connection
            let rt = tokio::runtime::Runtime::new().unwrap();
            let pool = rt.block_on(async {
                create_database_connection().await.expect("Failed to create database connection")
            });

            app.manage(pool);
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            // Authentication handlers
            login,
            logout,
            refresh_token,
            validate_token,
            get_current_user,
            // Category handlers
            get_categories,
            get_category_by_id,
            get_categories_with_children,
            create_category,
            update_category,
            // Supplier handlers
            get_suppliers,
            get_supplier_by_id,
            get_suppliers_with_stats,
            create_supplier,
            update_supplier,
            // Customer handlers
            get_customers,
            get_customer_by_id,
            get_customers_with_stats,
            create_customer,
            update_customer,
            search_customers_by_phone,
            search_customers_by_name,
            // Product handlers
            get_products,
            get_product_by_id,
            get_product_by_barcode,
            create_product,
            update_product,
            // Transaction handlers
            create_transaction,
            get_transactions,
            get_transaction_by_id,
            void_transaction,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
