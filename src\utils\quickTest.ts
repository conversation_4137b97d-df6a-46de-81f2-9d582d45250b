/**
 * Quick test utility to verify that the application works without mock data
 */

import { authService } from '@/services/authService';
import { productService } from '@/services/productService';
import { transactionService } from '@/services/transactionService';

export async function quickSystemTest() {
  console.log('🚀 Starting Quick System Test...');
  const tauriV2 = (window as any).__TAURI_INTERNALS__;
  const tauriV1 = (window as any).__TAURI__;
  console.log('Environment:', (tauriV2 || tauriV1) ? 'Tauri' : 'Browser');

  try {
    // Test 1: Authentication
    console.log('\n1️⃣ Testing Authentication...');
    const loginResult = await authService.login({
      username: 'admin',
      password: 'admin123'
    });
    console.log('✅ Login successful:', loginResult.user.username);

    // Test 2: Product fetching
    console.log('\n2️⃣ Testing Product Fetching...');
    const products = await productService.getProducts();
    console.log(`✅ Fetched ${products.length} products`);

    if (products.length > 0) {
      console.log('First product:', products[0].name);
    }

    // Test 3: Transaction fetching
    console.log('\n3️⃣ Testing Transaction Fetching...');
    const transactions = await transactionService.getTransactions({ limit: 5 });
    console.log(`✅ Fetched ${transactions.length} transactions`);

    // Test 4: Product creation
    console.log('\n4️⃣ Testing Product Creation...');
    const testProduct = await productService.createProduct({
      sku: `QUICK-TEST-${Date.now()}`,
      name: 'Quick Test Product',
      description: 'Product created during quick test',
      costPrice: 1000,
      sellingPrice: 1500,
      currentStock: 50,
      minStock: 5,
      unit: 'pcs',
      isTrackable: true,
      allowNegativeStock: false,
      isTaxable: true,
    });
    console.log('✅ Product created:', testProduct.name, 'ID:', testProduct.id);

    // Test 5: Transaction creation
    console.log('\n5️⃣ Testing Transaction Creation...');
    const transaction = await transactionService.createTransaction({
      customerName: 'Quick Test Customer',
      items: [{
        productId: testProduct.id,
        quantity: 2,
        unitPrice: testProduct.sellingPrice,
        discountAmount: 0,
      }],
      payments: [{
        paymentMethodId: 1, // Cash
        amount: testProduct.sellingPrice * 2,
      }],
      discountAmount: 0,
      discountPercentage: 0,
      taxPercentage: 0,
      notes: 'Quick test transaction',
    }, loginResult.user.id);
    console.log('✅ Transaction created:', transaction.transaction.transactionNumber);

    console.log('\n🎉 All tests passed! The system is working correctly without mock data.');

    return {
      success: true,
      message: 'All tests passed successfully',
      data: {
        user: loginResult.user,
        productCount: products.length,
        transactionCount: transactions.length,
        testProduct,
        testTransaction: transaction.transaction,
      }
    };

  } catch (error) {
    console.error('❌ Test failed:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error',
      error
    };
  }
}

// Auto-run test when this module is imported in development
if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
  // Add a small delay to ensure the app is fully loaded
  setTimeout(() => {
    console.log('Auto-running quick system test...');
    quickSystemTest().then(result => {
      if (result.success) {
        console.log('✅ Quick test completed successfully');
      } else {
        console.error('❌ Quick test failed:', result.message);
      }
    });
  }, 2000);
}
