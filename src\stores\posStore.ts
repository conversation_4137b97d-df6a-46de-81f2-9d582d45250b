import { create } from 'zustand';
import { Product } from '@/types';
import { calculateTax, calculateDiscount } from '@/lib/utils';
import { transactionService, CreateTransactionRequest } from '@/services/transactionService';
import { useAuthStore } from '@/stores/authStore';

export type DiscountMode = 'percentage' | 'amount';

export interface CartItem {
  product: Product;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  discount?: number;
  addedAt: Date; // Add timestamp for sorting
}

export interface PaymentMethod {
  id: string;
  name: string;
  type: 'cash' | 'card' | 'digital';
}

export interface Transaction {
  id: string;
  transactionNumber: string;
  items: CartItem[];
  subtotal: number;
  discountAmount: number;
  discountPercentage: number;
  discountMode: DiscountMode;
  taxAmount: number;
  taxPercentage: number;
  totalAmount: number;
  paidAmount: number;
  changeAmount: number;
  paymentMethod: PaymentMethod;
  customerName?: string;
  customerContact?: string;
  notes?: string;
  status: 'completed' | 'pending';
  createdAt: Date;
}

export interface Cart {
  id: string;
  name: string;
  items: CartItem[];
  discountPercentage: number;
  discountAmount: number;
  discountMode: DiscountMode;
  customerName: string;
  customerContact: string;
  notes: string;
  createdAt: Date;
}

interface POSState {
  // Multi-cart support
  carts: Cart[];
  activeCartId: string | null;

  // Legacy single cart support (computed from active cart)
  cart: CartItem[];
  currentTransaction: Transaction | null;
  discountPercentage: number;
  discountAmount: number;
  discountMode: DiscountMode;
  taxPercentage: number;
  customerName: string;
  customerContact: string;
  notes: string;
  isProcessing: boolean;

  // Pending transactions
  pendingTransactions: Transaction[];
}

interface POSActions {
  // Multi-cart management
  createNewCart: (name?: string) => string;
  switchToCart: (cartId: string) => void;
  deleteCart: (cartId: string) => void;
  renameCart: (cartId: string, name: string) => void;

  // Legacy cart actions (work on active cart)
  addToCart: (product: Product, quantity?: number) => void;
  removeFromCart: (productId: number) => void;
  updateQuantity: (productId: number, quantity: number) => void;
  clearCart: () => void;
  clearCurrentTransaction: () => void;
  setDiscount: (percentage: number) => void;
  setDiscountAmount: (amount: number) => void;
  setDiscountMode: (mode: DiscountMode) => void;
  setTax: (percentage: number) => void;
  setCustomerName: (name: string) => void;
  setCustomerContact: (contact: string) => void;
  setNotes: (notes: string) => void;

  // Payment processing
  processPayment: (paidAmount: number, paymentMethod: PaymentMethod, isPending?: boolean) => Promise<Transaction>;
  completePendingPayment: (transactionId: string, paidAmount: number, paymentMethod: PaymentMethod) => Promise<Transaction>;

  // Utility functions
  getCartSummary: () => {
    subtotal: number;
    discountAmount: number;
    taxAmount: number;
    totalAmount: number;
    itemCount: number;
  };
  getActiveCart: () => Cart | null;
}

type POSStore = POSState & POSActions;

export const usePOSStore = create<POSStore>((set, get) => {
  // Helper function to generate cart ID
  const generateCartId = () => `cart_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

  // Helper function to create a new cart
  const createCart = (name?: string): Cart => ({
    id: generateCartId(),
    name: name || `Cart ${Date.now()}`,
    items: [],
    discountPercentage: 0,
    discountAmount: 0,
    discountMode: 'percentage',
    customerName: '',
    customerContact: '',
    notes: '',
    createdAt: new Date(),
  });

  const initialCart = createCart('Main Cart');

  return {
    // State
    carts: [initialCart],
    activeCartId: initialCart.id,
    cart: [],
    currentTransaction: null,
    discountPercentage: 0,
    discountAmount: 0,
    discountMode: 'percentage',
    taxPercentage: 10, // Default 10% tax
    customerName: '',
    customerContact: '',
    notes: '',
    isProcessing: false,
    pendingTransactions: [],

    // Multi-cart management actions
    createNewCart: (name?: string) => {
      const { carts } = get();
      const newCart = createCart(name);
      set({
        carts: [...carts, newCart],
        activeCartId: newCart.id
      });
      // Update legacy cart state
      get().switchToCart(newCart.id);
      return newCart.id;
    },

    switchToCart: (cartId: string) => {
      const { carts } = get();
      const targetCart = carts.find(c => c.id === cartId);
      if (!targetCart) return;

      set({
        activeCartId: cartId,
        cart: targetCart.items,
        discountPercentage: targetCart.discountPercentage,
        discountAmount: targetCart.discountAmount,
        discountMode: targetCart.discountMode,
        customerName: targetCart.customerName,
        customerContact: targetCart.customerContact,
        notes: targetCart.notes,
      });
    },

    deleteCart: (cartId: string) => {
      const { carts, activeCartId } = get();
      if (carts.length <= 1) return; // Don't delete the last cart

      const updatedCarts = carts.filter(c => c.id !== cartId);
      const newActiveCartId = activeCartId === cartId ? updatedCarts[0]?.id || null : activeCartId;

      set({ carts: updatedCarts, activeCartId: newActiveCartId });

      if (newActiveCartId) {
        get().switchToCart(newActiveCartId);
      }
    },

    renameCart: (cartId: string, name: string) => {
      const { carts } = get();
      const updatedCarts = carts.map(cart =>
        cart.id === cartId ? { ...cart, name } : cart
      );
      set({ carts: updatedCarts });
    },

    getActiveCart: () => {
      const { carts, activeCartId } = get();
      return carts.find(c => c.id === activeCartId) || null;
    },

    // Legacy cart actions (work on active cart)
    addToCart: (product: Product, quantity = 1) => {
      const { cart, carts, activeCartId } = get();
      const existingItem = cart.find(item => item.product.id === product.id);

      let updatedCart: CartItem[];
      if (existingItem) {
        // Update existing item
        updatedCart = cart.map(item =>
          item.product.id === product.id
            ? {
              ...item,
              quantity: item.quantity + quantity,
              totalPrice: (item.quantity + quantity) * item.unitPrice,
            }
            : item
        );
      } else {
        // Add new item
        const newItem: CartItem = {
          product,
          quantity,
          unitPrice: product.sellingPrice,
          totalPrice: quantity * product.sellingPrice,
          discount: 0,
          addedAt: new Date(),
        };
        updatedCart = [...cart, newItem];
      }

      // Update both legacy cart and multi-cart state
      set({ cart: updatedCart });

      if (activeCartId) {
        const updatedCarts = carts.map(c =>
          c.id === activeCartId ? { ...c, items: updatedCart } : c
        );
        set({ carts: updatedCarts });
      }
    },

    removeFromCart: (productId: number) => {
      const { cart, carts, activeCartId } = get();
      const updatedCart = cart.filter(item => item.product.id !== productId);
      set({ cart: updatedCart });

      if (activeCartId) {
        const updatedCarts = carts.map(c =>
          c.id === activeCartId ? { ...c, items: updatedCart } : c
        );
        set({ carts: updatedCarts });
      }
    },

    updateQuantity: (productId: number, quantity: number) => {
      if (quantity <= 0) {
        get().removeFromCart(productId);
        return;
      }

      const { cart, carts, activeCartId } = get();
      const updatedCart = cart.map(item =>
        item.product.id === productId
          ? {
            ...item,
            quantity,
            totalPrice: quantity * item.unitPrice,
          }
          : item
      );
      set({ cart: updatedCart });

      if (activeCartId) {
        const updatedCarts = carts.map(c =>
          c.id === activeCartId ? { ...c, items: updatedCart } : c
        );
        set({ carts: updatedCarts });
      }
    },

    clearCart: () => {
      const { carts, activeCartId } = get();
      set({
        cart: [],
        discountPercentage: 0,
        discountAmount: 0,
        discountMode: 'percentage',
        customerName: '',
        customerContact: '',
        notes: '',
        // Don't clear currentTransaction here - it's needed for the receipt modal
      });

      if (activeCartId) {
        const updatedCarts = carts.map(c =>
          c.id === activeCartId
            ? { ...c, items: [], discountPercentage: 0, discountAmount: 0, discountMode: 'percentage' as DiscountMode, customerName: '', customerContact: '', notes: '' }
            : c
        );
        set({ carts: updatedCarts });
      }
    },

    clearCurrentTransaction: () => {
      set({ currentTransaction: null });
    },

    setDiscount: (percentage: number) => {
      const { carts, activeCartId } = get();
      const discountPercentage = Math.max(0, Math.min(100, percentage));
      set({ discountPercentage });

      if (activeCartId) {
        const updatedCarts = carts.map(c =>
          c.id === activeCartId ? { ...c, discountPercentage } : c
        );
        set({ carts: updatedCarts });
      }
    },

    setDiscountAmount: (amount: number) => {
      const { carts, activeCartId } = get();
      const discountAmount = Math.max(0, amount);
      set({ discountAmount });

      if (activeCartId) {
        const updatedCarts = carts.map(c =>
          c.id === activeCartId ? { ...c, discountAmount } : c
        );
        set({ carts: updatedCarts });
      }
    },

    setDiscountMode: (mode: DiscountMode) => {
      const { carts, activeCartId } = get();
      set({ discountMode: mode });

      if (activeCartId) {
        const updatedCarts = carts.map(c =>
          c.id === activeCartId ? { ...c, discountMode: mode } : c
        );
        set({ carts: updatedCarts });
      }
    },

    setTax: (percentage: number) => {
      set({ taxPercentage: Math.max(0, percentage) });
    },

    setCustomerName: (name: string) => {
      const { carts, activeCartId } = get();
      set({ customerName: name });

      if (activeCartId) {
        const updatedCarts = carts.map(c =>
          c.id === activeCartId ? { ...c, customerName: name } : c
        );
        set({ carts: updatedCarts });
      }
    },

    setCustomerContact: (contact: string) => {
      const { carts, activeCartId } = get();
      set({ customerContact: contact });

      if (activeCartId) {
        const updatedCarts = carts.map(c =>
          c.id === activeCartId ? { ...c, customerContact: contact } : c
        );
        set({ carts: updatedCarts });
      }
    },

    setNotes: (notes: string) => {
      const { carts, activeCartId } = get();
      set({ notes });

      if (activeCartId) {
        const updatedCarts = carts.map(c =>
          c.id === activeCartId ? { ...c, notes } : c
        );
        set({ carts: updatedCarts });
      }
    },

    processPayment: async (paidAmount: number, paymentMethod: PaymentMethod, isPending = false) => {
      const { cart, discountPercentage, discountMode, taxPercentage, customerName, customerContact, notes, pendingTransactions } = get();

      if (cart.length === 0) {
        throw new Error('Cart is empty');
      }

      set({ isProcessing: true });

      try {
        const summary = get().getCartSummary();

        // For pending payments, allow partial or zero payment
        if (!isPending && paidAmount < summary.totalAmount) {
          throw new Error('Insufficient payment amount');
        }

        // Get current user for cashier_id
        const currentUser = useAuthStore.getState().user;
        if (!currentUser) {
          throw new Error('User not authenticated');
        }

        // Prepare transaction request for backend
        const transactionRequest: CreateTransactionRequest = {
          customerName: customerName || undefined,
          items: cart.map(item => ({
            productId: item.product.id,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            discountAmount: item.discount || 0,
          })),
          discountAmount: summary.discountAmount,
          discountPercentage: discountPercentage,
          taxPercentage: taxPercentage,
          payments: isPending ? [] : [{
            paymentMethodId: 1, // TODO: Map payment method to actual IDs from backend
            amount: paidAmount,
            referenceNumber: undefined,
          }],
          notes: notes || undefined,
        };

        // Call backend API
        console.log('Cashier ID:', currentUser.id);
        const backendTransaction = await transactionService.createTransaction(
          transactionRequest,
          currentUser.id
        );

        // Convert backend transaction to local transaction format
        console.log('Backend transaction:', backendTransaction);
        const transaction: Transaction = {
          id: backendTransaction.transaction.id.toString(),
          transactionNumber: backendTransaction.transaction.transactionNumber,
          items: [...cart],
          subtotal: summary.subtotal,
          discountAmount: summary.discountAmount,
          discountPercentage,
          discountMode,
          taxAmount: summary.taxAmount,
          taxPercentage,
          totalAmount: summary.totalAmount,
          paidAmount: isPending ? 0 : paidAmount,
          changeAmount: isPending ? 0 : paidAmount - summary.totalAmount,
          paymentMethod,
          customerName: customerName || undefined,
          customerContact: customerContact || undefined,
          notes: notes || undefined,
          status: isPending ? 'pending' : 'completed',
          createdAt: new Date(backendTransaction.transaction.createdAt),
        };

        if (isPending) {
          // Add to pending transactions
          console.log('Setting pending transaction:', transaction);
          set({
            pendingTransactions: [...pendingTransactions, transaction],
            isProcessing: false,
          });
        } else {
          console.log('Setting currentTransaction:', transaction);
          set({
            currentTransaction: transaction,
            isProcessing: false,
          });
          console.log('currentTransaction set, state:', get().currentTransaction);
        }

        // Clear cart after successful transaction
        get().clearCart();

        return transaction;
      } catch (error) {
        set({ isProcessing: false });
        throw error;
      }
    },

    completePendingPayment: async (transactionId: string, paidAmount: number, paymentMethod: PaymentMethod) => {
      const { pendingTransactions } = get();
      const pendingTransaction = pendingTransactions.find(t => t.id === transactionId);

      if (!pendingTransaction) {
        throw new Error('Pending transaction not found');
      }

      if (paidAmount < pendingTransaction.totalAmount) {
        throw new Error('Insufficient payment amount');
      }

      set({ isProcessing: true });

      try {
        const completedTransaction: Transaction = {
          ...pendingTransaction,
          paidAmount,
          changeAmount: paidAmount - pendingTransaction.totalAmount,
          paymentMethod,
          status: 'completed',
        };

        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Remove from pending and set as current transaction
        const updatedPendingTransactions = pendingTransactions.filter(t => t.id !== transactionId);
        set({
          pendingTransactions: updatedPendingTransactions,
          currentTransaction: completedTransaction,
          isProcessing: false,
        });

        return completedTransaction;
      } catch (error) {
        set({ isProcessing: false });
        throw error;
      }
    },

    getCartSummary: () => {
      const { cart, discountPercentage, discountAmount, discountMode, taxPercentage } = get();

      const subtotal = cart.reduce((sum, item) => sum + item.totalPrice, 0);
      const computedDiscountAmount = discountMode === 'percentage'
        ? calculateDiscount(subtotal, discountPercentage)
        : Math.min(discountAmount, subtotal);
      const taxableAmount = subtotal - computedDiscountAmount;
      const taxAmount = calculateTax(taxableAmount, taxPercentage);
      const totalAmount = taxableAmount + taxAmount;
      const itemCount = cart.reduce((sum, item) => sum + item.quantity, 0);

      return {
        subtotal,
        discountAmount: computedDiscountAmount,
        taxAmount,
        totalAmount,
        itemCount,
      };
    },
  };
});
