import React, { useState } from 'react';
import { X, CreditCard, Banknote, Smartphone } from 'lucide-react';
import { toast } from 'sonner';

import { usePOSStore, PaymentMethod } from '@/stores/posStore';
import { formatCurrency, calculateChange } from '@/lib/utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/common/Card';
import Button from '@/components/common/Button';
import Input from '@/components/common/Input';
import Autocomplete, { AutocompleteOption } from '@/components/common/Autocomplete';
import { customerService, CreateCustomerRequest } from '@/services/customerService';
import { Customer } from '@/types';

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  totalAmount: number;
  onPendingPayment?: () => void;
}

const paymentMethods: PaymentMethod[] = [
  { id: 'cash', name: 'Cash', type: 'cash' },
  { id: 'card', name: 'Credit/Debit Card', type: 'card' },
  { id: 'transfer', name: 'Transfer Bank', type: 'digital' },
  { id: 'ewallet', name: 'GoPay/Ovo/Dana', type: 'digital' },
];

const quickAmounts = [50000, 100000, 200000, 500000];

const PaymentModal: React.FC<PaymentModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  totalAmount,
  onPendingPayment,
}) => {
  const {
    isProcessing,
    customerName,
    customerContact,
    processPayment,
    setCustomerName,
    setCustomerContact,
  } = usePOSStore();
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod>(paymentMethods[0]);
  const [paidAmount, setPaidAmount] = useState<string>(totalAmount.toString());

  // Customer autocomplete state
  const [selectedCustomer, setSelectedCustomer] = useState<AutocompleteOption | null>(null);
  const [showAddCustomerModal, setShowAddCustomerModal] = useState(false);
  const [newCustomerData, setNewCustomerData] = useState<CreateCustomerRequest>({
    name: '',
    phone: '',
    email: '',
    address: '',
  });

  const paidAmountNumber = parseFloat(paidAmount) || 0;
  const changeAmount = calculateChange(totalAmount, paidAmountNumber);
  const isInsufficientPayment = paidAmountNumber < totalAmount;

  // Customer search function
  const searchCustomers = async (query: string): Promise<AutocompleteOption[]> => {
    try {
      const customers = await customerService.searchCustomersByName(query);
      return customers.map(customer => ({
        id: customer.id,
        label: `${customer.name}${customer.phone ? ` (${customer.phone})` : ''}`,
        value: customer,
      }));
    } catch (error) {
      console.error('Error searching customers:', error);
      return [];
    }
  };

  // Handle customer selection
  const handleCustomerSelect = (option: AutocompleteOption | null) => {
    setSelectedCustomer(option);
    if (option) {
      const customer = option.value as Customer;
      setCustomerName(customer.name);
      setCustomerContact(customer.phone || customer.email || '');
    } else {
      setCustomerName('');
      setCustomerContact('');
    }
  };

  // Handle add new customer
  const handleAddNewCustomer = () => {
    setShowAddCustomerModal(true);
  };

  // Handle save new customer
  const handleSaveNewCustomer = async () => {
    try {
      if (!newCustomerData.name.trim()) {
        toast.error('Customer name is required');
        return;
      }

      const newCustomer = await customerService.createCustomer(newCustomerData);

      // Select the newly created customer
      const customerOption: AutocompleteOption = {
        id: newCustomer.id,
        label: `${newCustomer.name}${newCustomer.phone ? ` (${newCustomer.phone})` : ''}`,
        value: newCustomer,
      };

      handleCustomerSelect(customerOption);
      setShowAddCustomerModal(false);
      setNewCustomerData({ name: '', phone: '', email: '', address: '' });
      toast.success('Customer added successfully');
    } catch (error) {
      console.error('Error creating customer:', error);
      if (error instanceof Error && error.message.includes('phone number already exists')) {
        toast.error('A customer with this phone number already exists');
      } else {
        toast.error('Failed to create customer');
      }
    }
  };

  const handlePayment = async () => {
    if (isInsufficientPayment) {
      toast.error('Insufficient payment amount');
      return;
    }

    try {
      console.log('Processing payment...');
      const result = await processPayment(paidAmountNumber, selectedPaymentMethod, false);
      console.log('Payment processed, result:', result);
      console.log('Calling onSuccess');
      onSuccess();
      console.log('onSuccess called');
    } catch (error) {
      console.error('Payment error:', error);
      toast.error(error instanceof Error ? error.message : 'Payment failed');
    }
  };

  const handlePendingPayment = async () => {
    try {
      await processPayment(0, selectedPaymentMethod, true);
      onPendingPayment?.();
      toast.success('Transaction saved as pending payment');
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to save pending payment');
    }
  };

  const handleQuickAmount = (amount: number) => {
    setPaidAmount(amount.toString());
  };

  const getPaymentMethodIcon = (type: string) => {
    switch (type) {
      case 'cash':
        return <Banknote className="h-5 w-5" />;
      case 'card':
        return <CreditCard className="h-5 w-5" />;
      case 'digital':
        return <Smartphone className="h-5 w-5" />;
      default:
        return <CreditCard className="h-5 w-5" />;
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-md max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Payment</CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Total Amount */}
          <div className="text-center p-4 bg-primary-50 rounded-lg">
            <p className="text-sm text-primary-600 mb-1">Total Amount</p>
            <p className="text-2xl font-bold text-primary-900">
              {formatCurrency(totalAmount)}
            </p>
          </div>

          {/* Customer Information */}
          <div className="pt-4 border-t space-y-3">
            <Autocomplete
              label="Customer"
              placeholder="Search customers or add new..."
              value={selectedCustomer}
              onSearch={searchCustomers}
              onChange={handleCustomerSelect}
              onAddNew={handleAddNewCustomer}
              addNewLabel="Add New Customer"
              noOptionsMessage="No customers found"
            />
            {selectedCustomer && (
              <div className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
                <p><strong>Name:</strong> {customerName}</p>
                {customerContact && <p><strong>Contact:</strong> {customerContact}</p>}
              </div>
            )}
          </div>

          {/* Payment Method Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Payment Method
            </label>
            <div className="grid grid-cols-2 gap-2">
              {paymentMethods.map((method) => (
                <button
                  key={method.id}
                  onClick={() => setSelectedPaymentMethod(method)}
                  className={`flex items-center p-3 rounded-lg border-2 transition-colors ${
                    selectedPaymentMethod.id === method.id
                      ? 'border-primary-500 bg-primary-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  {getPaymentMethodIcon(method.type)}
                  <span className="ml-3 font-medium">{method.name}</span>
                </button>
              ))}
            </div>
          </div>

          {/* Payment Amount */}
          {selectedPaymentMethod.type === 'cash' && (
            <div>
              <Input
                type="number"
                label="Amount Paid"
                value={paidAmount}
                onChange={(e) => setPaidAmount(e.target.value)}
                placeholder="0"
                min="0"
                step="1000"
                error={isInsufficientPayment ? 'Insufficient payment amount' : undefined}
              />

              {/* Quick Amount Buttons */}
              <div className="mt-3">
                <p className="text-sm font-medium text-gray-700 mb-2">Quick amounts:</p>
                <div className="grid grid-cols-2 gap-2">
                  {quickAmounts.map((amount) => (
                    <Button
                      key={amount}
                      variant="outline"
                      size="sm"
                      onClick={() => handleQuickAmount(amount)}
                      className="text-xs"
                    >
                      {formatCurrency(amount)}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Change Amount */}
              {paidAmountNumber > 0 && (
                <div className="mt-4 p-3 bg-green-50 rounded-lg">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-green-700">Change:</span>
                    <span className="text-lg font-bold text-green-900">
                      {formatCurrency(changeAmount)}
                    </span>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Digital Payment Info */}
          {selectedPaymentMethod.type === 'digital' && (
            <div className="p-4 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-700">
                Customer will pay exactly {formatCurrency(totalAmount)} via {selectedPaymentMethod.name}
              </p>
            </div>
          )}

          {/* Card Payment Info */}
          {selectedPaymentMethod.type === 'card' && (
            <div className="p-4 bg-purple-50 rounded-lg">
              <p className="text-sm text-purple-700">
                Customer will pay exactly {formatCurrency(totalAmount)} via card
              </p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="space-y-3">
            {onPendingPayment && (
              <Button
                variant="outline"
                onClick={handlePendingPayment}
                className="w-full"
                disabled={isProcessing}
              >
                Save as Pending Payment
              </Button>
            )}
            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={onClose}
                className="flex-1"
                disabled={isProcessing}
              >
                Cancel
              </Button>
              <Button
                onClick={handlePayment}
                className="flex-1"
                loading={isProcessing}
                disabled={isInsufficientPayment || isProcessing}
              >
                {isProcessing ? 'Processing...' : 'Complete Payment'}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Add New Customer Modal */}
      {showAddCustomerModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md mx-4">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Add New Customer
                <button
                  onClick={() => setShowAddCustomerModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-5 w-5" />
                </button>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Input
                type="text"
                label="Customer Name"
                placeholder="Enter customer name"
                value={newCustomerData.name}
                onChange={(e) => setNewCustomerData(prev => ({ ...prev, name: e.target.value }))}
                required
              />
              <Input
                type="text"
                label="Phone Number"
                placeholder="Enter phone number"
                value={newCustomerData.phone}
                onChange={(e) => setNewCustomerData(prev => ({ ...prev, phone: e.target.value }))}
              />
              <Input
                type="email"
                label="Email"
                placeholder="Enter email address"
                value={newCustomerData.email}
                onChange={(e) => setNewCustomerData(prev => ({ ...prev, email: e.target.value }))}
              />
              <Input
                type="text"
                label="Address"
                placeholder="Enter address"
                value={newCustomerData.address}
                onChange={(e) => setNewCustomerData(prev => ({ ...prev, address: e.target.value }))}
              />
              <div className="flex space-x-3 pt-4">
                <Button
                  variant="outline"
                  onClick={() => setShowAddCustomerModal(false)}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleSaveNewCustomer}
                  className="flex-1"
                >
                  Add Customer
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default PaymentModal;
