import { invoke } from '@tauri-apps/api/core';
import { ApiResponse } from '@/types';
import { getCurrentEnvironment } from '@/utils/environment';
import { mockApiService } from './mockApiService';

// Transaction types matching the Rust backend
export interface Transaction {
  id: number;
  transactionNumber: string;
  transactionDate: string;
  transactionType: 'sale' | 'return' | 'void';
  customerId?: number;
  customerName?: string;
  subtotal: number;
  discountAmount: number;
  discountPercentage: number;
  taxAmount: number;
  taxPercentage: number;
  totalAmount: number;
  paidAmount: number;
  changeAmount: number;
  status: 'pending' | 'completed' | 'cancelled' | 'refunded';
  paymentStatus: 'unpaid' | 'partial' | 'paid' | 'overpaid';
  cashierId: number;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface TransactionItem {
  id: number;
  transactionId: number;
  productId: number;
  productName: string;
  productSku: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  discountAmount: number;
  createdAt: string;
}

export interface PaymentMethod {
  id: number;
  name: string;
  paymentType: 'cash' | 'card' | 'digital';
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface TransactionPayment {
  id: number;
  transactionId: number;
  paymentMethodId: number;
  amount: number;
  referenceNumber?: string;
  createdAt: string;
}

export interface TransactionPaymentWithMethod {
  payment: TransactionPayment;
  paymentMethod: PaymentMethod;
}

export interface TransactionWithDetails {
  transaction: Transaction;
  items: TransactionItem[];
  payments: TransactionPaymentWithMethod[];
}

export interface CreateTransactionRequest {
  customerId?: number;
  customerName?: string;
  items: CreateTransactionItemRequest[];
  discountAmount?: number;
  discountPercentage?: number;
  taxPercentage?: number;
  payments: CreateTransactionPaymentRequest[];
  notes?: string;
}

export interface CreateTransactionItemRequest {
  productId: number;
  quantity: number;
  unitPrice: number;
  discountAmount?: number;
}

export interface CreateTransactionPaymentRequest {
  paymentMethodId: number;
  amount: number;
  referenceNumber?: string;
}

export interface GetTransactionsParams {
  limit?: number;
  offset?: number;
  startDate?: string;
  endDate?: string;
  customerId?: number;
  status?: string;
}

export class TransactionService {
  private static instance: TransactionService;

  private constructor() { }

  public static getInstance(): TransactionService {
    if (!TransactionService.instance) {
      TransactionService.instance = new TransactionService();
    }
    return TransactionService.instance;
  }

  /**
   * Create a new transaction
   */
  public async createTransaction(
    request: CreateTransactionRequest,
    cashierId: number
  ): Promise<TransactionWithDetails> {
    try {
      const environment = getCurrentEnvironment();

      if (environment === 'browser') {
        // Use mock API for browser environment
        const response = await mockApiService.createTransaction(request, cashierId);
        return response;
      } else {
        // Use Tauri backend for native app
        console.log('Create transaction request:', request);
        const response = await invoke<ApiResponse<TransactionWithDetails>>('create_transaction', {
          request,
          cashierId: cashierId,
        });

        console.log('Create transaction response:', response);
        if (!response.success || !response.data) {
          throw new Error(response.error || 'Failed to create transaction');
        }

        console.log('Transaction created response:', response);
        console.log('Transaction created successfully:', response.data);
        return response.data;
      }
    } catch (error) {
      console.error('Create transaction error:', error);
      throw new Error('Failed to create transaction. Please try again.');
    }
  }

  /**
   * Get transactions with optional filtering
   */
  public async getTransactions(params?: GetTransactionsParams): Promise<Transaction[]> {
    try {
      const response = await invoke<ApiResponse<Transaction[]>>('get_transactions', {
        limit: params?.limit,
        offset: params?.offset,
        start_date: params?.startDate,
        end_date: params?.endDate,
        customer_id: params?.customerId,
        status: params?.status,
      });

      if (!response.success || !response.data) {
        throw new Error(response.error || 'Failed to fetch transactions');
      }

      return response.data;
    } catch (error) {
      console.error('Get transactions error:', error);
      throw new Error('Failed to fetch transactions. Please try again.');
    }
  }

  /**
   * Get transaction by ID with full details
   */
  public async getTransactionById(transactionId: number): Promise<TransactionWithDetails> {
    try {
      const response = await invoke<ApiResponse<TransactionWithDetails>>('get_transaction_by_id', {
        transaction_id: transactionId,
      });

      if (!response.success || !response.data) {
        throw new Error(response.error || 'Transaction not found');
      }

      return response.data;
    } catch (error) {
      console.error('Get transaction by ID error:', error);
      throw new Error('Failed to fetch transaction details. Please try again.');
    }
  }

  /**
   * Void a transaction (cancel and restore inventory)
   */
  public async voidTransaction(
    transactionId: number,
    cashierId: number,
    reason?: string
  ): Promise<Transaction> {
    try {
      const response = await invoke<ApiResponse<Transaction>>('void_transaction', {
        transaction_id: transactionId,
        cashier_id: cashierId,
        reason,
      });

      if (!response.success || !response.data) {
        throw new Error(response.error || 'Failed to void transaction');
      }

      return response.data;
    } catch (error) {
      console.error('Void transaction error:', error);
      throw new Error('Failed to void transaction. Please try again.');
    }
  }

  /**
   * Helper method to calculate transaction totals
   */
  public calculateTransactionTotals(
    items: CreateTransactionItemRequest[],
    discountAmount: number = 0,
    discountPercentage: number = 0,
    taxPercentage: number = 0
  ): {
    subtotal: number;
    totalDiscount: number;
    taxAmount: number;
    totalAmount: number;
  } {
    const subtotal = items.reduce((sum, item) => {
      const itemTotal = item.quantity * item.unitPrice - (item.discountAmount || 0);
      return sum + itemTotal;
    }, 0);

    const discountFromPercentage = subtotal * (discountPercentage / 100);
    const totalDiscount = discountAmount + discountFromPercentage;
    const afterDiscount = subtotal - totalDiscount;
    const taxAmount = afterDiscount * (taxPercentage / 100);
    const totalAmount = afterDiscount + taxAmount;

    return {
      subtotal,
      totalDiscount,
      taxAmount,
      totalAmount,
    };
  }

  /**
   * Helper method to validate payment amounts
   */
  public validatePayments(
    payments: CreateTransactionPaymentRequest[],
    totalAmount: number
  ): {
    isValid: boolean;
    totalPaid: number;
    changeAmount: number;
    error?: string;
  } {
    const totalPaid = payments.reduce((sum, payment) => sum + payment.amount, 0);
    const changeAmount = totalPaid - totalAmount;

    if (totalPaid < totalAmount) {
      return {
        isValid: false,
        totalPaid,
        changeAmount: 0,
        error: `Insufficient payment. Total: ${totalAmount.toFixed(2)}, Paid: ${totalPaid.toFixed(2)}`,
      };
    }

    return {
      isValid: true,
      totalPaid,
      changeAmount: Math.max(0, changeAmount),
    };
  }
}

// Export singleton instance
export const transactionService = TransactionService.getInstance();
