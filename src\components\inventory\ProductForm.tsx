import React, { useEffect, useState } from 'react';
import { use<PERSON><PERSON>, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { X, Save, Package } from 'lucide-react';
import { useMutation, useQuery } from '@tanstack/react-query';
import { toast } from 'sonner';

import { Product } from '@/types';
import { productService, CreateProductRequest, UpdateProductRequest } from '@/services/productService';
import { categoryService } from '@/services/categoryService';
import { supplierService } from '@/services/supplierService';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/common/Card';
import Input from '@/components/common/Input';
import Button from '@/components/common/Button';
import Autocomplete, { AutocompleteOption } from '@/components/common/Autocomplete';

const productSchema = z.object({
  sku: z.string().min(1, 'SKU is required'),
  barcode: z.string().optional(),
  name: z.string().min(1, 'Product name is required'),
  description: z.string().optional(),
  categoryId: z.number().nullable().optional(),
  supplierId: z.number().nullable().optional(),
  costPrice: z.number().min(0, 'Cost price must be positive'),
  sellingPrice: z.number().min(0, 'Selling price must be positive'),
  wholesalePrice: z.number().min(0).optional(),
  minSellingPrice: z.number().min(0).optional(),
  currentStock: z.number().min(0, 'Current stock must be positive'),
  minStock: z.number().min(0, 'Minimum stock must be positive'),
  maxStock: z.number().min(0).optional(),
  unit: z.string().min(1, 'Unit is required'),
  weight: z.number().min(0).optional(),
  dimensions: z.string().optional(),
  isTrackable: z.boolean().default(true),
  allowNegativeStock: z.boolean().default(false),
  isTaxable: z.boolean().default(true),
});

type ProductFormData = z.infer<typeof productSchema>;

interface ProductFormProps {
  product?: Product | null;
  onSave: () => void;
  onCancel: () => void;
}

const ProductForm: React.FC<ProductFormProps> = ({ product, onSave, onCancel }) => {
  const isEditing = !!product;

  // Fetch categories and suppliers
  const { data: categories = [] } = useQuery({
    queryKey: ['categories'],
    queryFn: categoryService.getCategories,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  const { data: suppliers = [] } = useQuery({
    queryKey: ['suppliers'],
    queryFn: supplierService.getSuppliers,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  // Autocomplete state
  const [selectedCategory, setSelectedCategory] = useState<AutocompleteOption | null>(null);
  const [selectedSupplier, setSelectedSupplier] = useState<AutocompleteOption | null>(null);

  // Search functions for autocomplete
  const searchCategories = async (query: string): Promise<AutocompleteOption[]> => {
    const filteredCategories = categories.filter(category =>
      category.name.toLowerCase().includes(query.toLowerCase())
    );
    return filteredCategories.map(category => ({
      id: category.id,
      label: category.name,
      value: category,
    }));
  };

  const searchSuppliers = async (query: string): Promise<AutocompleteOption[]> => {
    const filteredSuppliers = suppliers.filter(supplier =>
      supplier.name.toLowerCase().includes(query.toLowerCase())
    );
    return filteredSuppliers.map(supplier => ({
      id: supplier.id,
      label: supplier.name,
      value: supplier,
    }));
  };

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
  } = useForm({
    resolver: zodResolver(productSchema),
    defaultValues: {
      sku: '',
      barcode: '',
      name: '',
      description: '',
      categoryId: undefined,
      supplierId: undefined,
      costPrice: 0,
      sellingPrice: 0,
      wholesalePrice: 0,
      minSellingPrice: 0,
      currentStock: 0,
      minStock: 0,
      maxStock: 0,
      unit: 'pcs',
      weight: 0,
      dimensions: '',
      isTrackable: true,
      allowNegativeStock: false,
      isTaxable: true,
    },
  });



  // Populate form when editing
  useEffect(() => {
    if (product) {
      reset({
        sku: product.sku,
        barcode: product.barcode || '',
        name: product.name,
        description: product.description || '',
        categoryId: product.categoryId,
        supplierId: product.supplierId,
        costPrice: product.costPrice,
        sellingPrice: product.sellingPrice,
        wholesalePrice: product.wholesalePrice || 0,
        minSellingPrice: product.minSellingPrice || 0,
        currentStock: product.currentStock,
        minStock: product.minStock,
        maxStock: product.maxStock || 0,
        unit: product.unit,
        weight: product.weight || 0,
        dimensions: product.dimensions || '',
        isTrackable: product.isTrackable,
        allowNegativeStock: product.allowNegativeStock,
        isTaxable: product.isTaxable,
      });

      // Set autocomplete values
      if (product.categoryId && categories.length > 0) {
        const category = categories.find(c => c.id === product.categoryId);
        if (category) {
          setSelectedCategory({
            id: category.id,
            label: category.name,
            value: category,
          });
        }
      }

      if (product.supplierId && suppliers.length > 0) {
        const supplier = suppliers.find(s => s.id === product.supplierId);
        if (supplier) {
          setSelectedSupplier({
            id: supplier.id,
            label: supplier.name,
            value: supplier,
          });
        }
      }
    }
  }, [product, reset, categories, suppliers]);

  // Create product mutation
  const createMutation = useMutation({
    mutationFn: (data: CreateProductRequest) => productService.createProduct(data),
    onSuccess: () => {
      toast.success('Product created successfully');
      onSave();
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to create product');
    },
  });

  // Update product mutation
  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateProductRequest }) =>
      productService.updateProduct(id, data),
    onSuccess: () => {
      toast.success('Product updated successfully');
      onSave();
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to update product');
    },
  });

  const onSubmit: SubmitHandler<ProductFormData> = (data: any) => {
    // Convert form data to API format
    const apiData: any = {
      sku: data.sku,
      barcode: data.barcode || undefined,
      name: data.name,
      description: data.description || undefined,
      categoryId: data.categoryId || undefined,
      supplierId: data.supplierId || undefined,
      costPrice: data.costPrice,
      sellingPrice: data.sellingPrice,
      wholesalePrice: data.wholesalePrice || undefined,
      minSellingPrice: data.minSellingPrice || undefined,
      currentStock: data.currentStock,
      minStock: data.minStock,
      maxStock: data.maxStock || undefined,
      unit: data.unit,
      weight: data.weight || undefined,
      dimensions: data.dimensions || undefined,
      imagePath: undefined, // TODO: Add image upload support
      isTrackable: data.isTrackable,
      allowNegativeStock: data.allowNegativeStock,
      isTaxable: data.isTaxable,
    };

    if (isEditing && product) {
      updateMutation.mutate({ id: product.id, data: apiData });
    } else {
      createMutation.mutate(apiData);
    }
  };

  const isLoading = createMutation.isPending || updateMutation.isPending;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <Card className="border-0 shadow-none">
          <CardHeader className="!flex !flex-row items-center justify-between !space-y-0 pb-4 border-b">
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              {isEditing ? 'Edit Product' : 'Add New Product'}
            </CardTitle>
            <Button variant="outline" size="sm" onClick={onCancel}>
              <X className="h-4 w-4" />
            </Button>
          </CardHeader>
          
          <CardContent className="p-6">
            <form onSubmit={handleSubmit(onSubmit as any)} className="space-y-6">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    SKU *
                  </label>
                  <Input
                    {...register('sku')}
                    placeholder="Enter SKU"
                    error={errors.sku?.message}
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Barcode
                  </label>
                  <Input
                    {...register('barcode')}
                    placeholder="Enter barcode"
                    error={errors.barcode?.message}
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Product Name *
                </label>
                <Input
                  {...register('name')}
                  placeholder="Enter product name"
                  error={errors.name?.message}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  {...register('description')}
                  placeholder="Enter product description"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  rows={3}
                />
              </div>

              {/* Category and Supplier */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Autocomplete
                    label="Category"
                    placeholder="Search categories..."
                    value={selectedCategory}
                    onSearch={searchCategories}
                    onChange={(option) => {
                      setSelectedCategory(option);
                      // Update form value
                      setValue('categoryId', option?.value.id || undefined);
                    }}
                    error={errors.categoryId?.message}
                    required
                  />
                </div>

                <div>
                  <Autocomplete
                    label="Supplier"
                    placeholder="Search suppliers..."
                    value={selectedSupplier}
                    onSearch={searchSuppliers}
                    onChange={(option) => {
                      setSelectedSupplier(option);
                      // Update form value
                      setValue('supplierId', option?.value.id || undefined);
                    }}
                    error={errors.supplierId?.message}
                  />
                </div>
              </div>

              {/* Pricing */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Cost Price *
                  </label>
                  <Input
                    type="number"
                    step="0.01"
                    {...register('costPrice', { valueAsNumber: true })}
                    placeholder="0.00"
                    error={errors.costPrice?.message}
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Selling Price *
                  </label>
                  <Input
                    type="number"
                    step="0.01"
                    {...register('sellingPrice', { valueAsNumber: true })}
                    placeholder="0.00"
                    error={errors.sellingPrice?.message}
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Wholesale Price
                  </label>
                  <Input
                    type="number"
                    step="0.01"
                    {...register('wholesalePrice', { valueAsNumber: true })}
                    placeholder="0.00"
                    error={errors.wholesalePrice?.message}
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Min Selling Price
                  </label>
                  <Input
                    type="number"
                    step="0.01"
                    {...register('minSellingPrice', { valueAsNumber: true })}
                    placeholder="0.00"
                    error={errors.minSellingPrice?.message}
                  />
                </div>
              </div>

              {/* Stock Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Current Stock *
                  </label>
                  <Input
                    type="number"
                    step="0.01"
                    {...register('currentStock', { valueAsNumber: true })}
                    placeholder="0"
                    error={errors.currentStock?.message}
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Minimum Stock *
                  </label>
                  <Input
                    type="number"
                    step="0.01"
                    {...register('minStock', { valueAsNumber: true })}
                    placeholder="0"
                    error={errors.minStock?.message}
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Maximum Stock
                  </label>
                  <Input
                    type="number"
                    step="0.01"
                    {...register('maxStock', { valueAsNumber: true })}
                    placeholder="0"
                    error={errors.maxStock?.message}
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Unit *
                  </label>
                  <Input
                    {...register('unit')}
                    placeholder="pcs, kg, liter, etc."
                    error={errors.unit?.message}
                  />
                </div>
              </div>

              {/* Additional Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Weight (optional)
                  </label>
                  <Input
                    type="number"
                    step="0.01"
                    {...register('weight', { valueAsNumber: true })}
                    placeholder="0.00"
                    error={errors.weight?.message}
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Dimensions (optional)
                  </label>
                  <Input
                    {...register('dimensions')}
                    placeholder="L x W x H"
                    error={errors.dimensions?.message}
                  />
                </div>
              </div>

              {/* Settings */}
              <div className="space-y-3">
                <h3 className="text-lg font-medium text-gray-900">Settings</h3>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      {...register('isTrackable')}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Track inventory for this product</span>
                  </label>
                  
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      {...register('allowNegativeStock')}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Allow negative stock</span>
                  </label>
                  
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      {...register('isTaxable')}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">This product is taxable</span>
                  </label>
                </div>
              </div>

              {/* Form Actions */}
              <div className="flex justify-end gap-3 pt-6 border-t">
                <Button type="button" variant="outline" onClick={onCancel}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isLoading} className="flex items-center gap-2">
                  <Save className="h-4 w-4" />
                  {isLoading ? 'Saving...' : isEditing ? 'Update Product' : 'Create Product'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ProductForm;
