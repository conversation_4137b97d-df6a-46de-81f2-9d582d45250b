import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { User } from '@/types';
import { authService } from '@/services/authService';

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

interface AuthActions {
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
  setUser: (user: User | null) => void;
  initializeAuth: () => Promise<void>;
}

interface LoginCredentials {
  username: string;
  password: string;
  rememberMe?: boolean;
}

type AuthStore = AuthState & AuthActions;

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // State
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      login: async (credentials: LoginCredentials) => {
        set({ isLoading: true, error: null });

        try {
          const response = await authService.login(credentials);

          // Store refresh token
          localStorage.setItem('refresh_token', response.refreshToken);

          set({
            user: response.user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Login failed',
          });
          throw error;
        }
      },

      logout: async () => {
        try {
          await authService.logout();
        } catch (error) {
          console.warn('Logout error:', error);
        } finally {
          set({
            user: null,
            isAuthenticated: false,
            error: null,
          });
        }
      },

      clearError: () => {
        set({ error: null });
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      setUser: (user: User | null) => {
        set({
          user,
          isAuthenticated: !!user,
        });
      },

      initializeAuth: async () => {
        const state = get();

        // If we have a persisted user but no token in authService, try to restore it
        if (state.isAuthenticated && state.user) {
          set({ isLoading: true });

          try {
            // Restore token from localStorage
            authService.restoreTokenFromStorage();

            // Validate the token
            const isValid = await authService.validateToken();

            if (isValid) {
              // Token is valid, get current user to ensure data is fresh
              const currentUser = await authService.getCurrentUser();
              if (currentUser) {
                set({
                  user: currentUser,
                  isAuthenticated: true,
                  isLoading: false,
                  error: null,
                });
                return;
              }
            }

            // Token is invalid or user not found, clear auth state
            set({
              user: null,
              isAuthenticated: false,
              isLoading: false,
              error: null,
            });
          } catch (error) {
            console.warn('Auth initialization failed:', error);
            set({
              user: null,
              isAuthenticated: false,
              isLoading: false,
              error: null,
            });
          }
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
