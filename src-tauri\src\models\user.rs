use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use chrono::{DateTime, Utc};

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct User {
    pub id: i64,
    pub username: String,
    pub email: Option<String>,
    #[serde(skip_serializing)]
    pub password_hash: String,
    #[serde(rename = "fullName")]
    pub full_name: String,
    pub role: UserRole,
    #[serde(rename = "isActive")]
    pub is_active: bool,
    #[serde(rename = "lastLogin")]
    pub last_login: Option<DateTime<Utc>>,
    #[serde(rename = "createdAt")]
    pub created_at: DateTime<Utc>,
    #[serde(rename = "updatedAt")]
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "TEXT")]
pub enum UserRole {
    #[sqlx(rename = "admin")]
    Admin,
    #[sqlx(rename = "manager")]
    Manager,
    #[sqlx(rename = "cashier")]
    Cashier,
}

impl std::fmt::Display for UserRole {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            UserRole::Admin => write!(f, "admin"),
            UserRole::Manager => write!(f, "manager"),
            UserRole::Cashier => write!(f, "cashier"),
        }
    }
}

#[derive(Debug, Deserialize)]
pub struct CreateUserRequest {
    pub username: String,
    pub email: Option<String>,
    pub password: String,
    #[serde(rename = "fullName")]
    pub full_name: String,
    pub role: UserRole,
}

#[derive(Debug, Deserialize)]
pub struct UpdateUserRequest {
    pub username: Option<String>,
    pub email: Option<String>,
    #[serde(rename = "fullName")]
    pub full_name: Option<String>,
    pub role: Option<UserRole>,
    #[serde(rename = "isActive")]
    pub is_active: Option<bool>,
}

#[derive(Debug, Deserialize)]
pub struct ChangePasswordRequest {
    #[serde(rename = "currentPassword")]
    pub current_password: String,
    #[serde(rename = "newPassword")]
    pub new_password: String,
}

#[derive(Debug, Deserialize)]
pub struct LoginRequest {
    pub username: String,
    pub password: String,
}

#[derive(Debug, Serialize)]
pub struct LoginResponse {
    pub user: User,
    pub token: String,
    pub refresh_token: String,
}

#[derive(Debug, Serialize)]
pub struct UserSession {
    pub user_id: i64,
    pub username: String,
    pub role: UserRole,
    pub exp: usize,
}

impl User {
    pub fn can_manage_users(&self) -> bool {
        matches!(self.role, UserRole::Admin)
    }

    pub fn can_manage_inventory(&self) -> bool {
        matches!(self.role, UserRole::Admin | UserRole::Manager)
    }

    pub fn can_process_transactions(&self) -> bool {
        matches!(self.role, UserRole::Admin | UserRole::Manager | UserRole::Cashier)
    }

    pub fn can_view_reports(&self) -> bool {
        matches!(self.role, UserRole::Admin | UserRole::Manager)
    }

    pub fn can_manage_settings(&self) -> bool {
        matches!(self.role, UserRole::Admin)
    }
}
