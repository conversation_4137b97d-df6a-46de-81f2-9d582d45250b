use crate::database::DbPool;
use crate::models::customer::{Customer, CreateCustomerRequest, UpdateCustomerRequest, CustomerWithStats};
use crate::utils::{AuthService, ApiResponse, AppError, AppResult, require_manager_or_admin};
use chrono::Utc;
use tauri::State;

#[tauri::command]
pub async fn get_customers(
    pool: State<'_, DbPool>,
    token: String,
) -> Result<ApiResponse<Vec<Customer>>, String> {
    let result = get_customers_internal(&pool, token).await;
    
    match result {
        Ok(customers) => Ok(ApiResponse::success(customers)),
        Err(e) => Ok(e.into()),
    }
}

async fn get_customers_internal(pool: &DbPool, token: String) -> AppResult<Vec<Customer>> {
    let _user_session = AuthService::extract_user_from_token(&token)?;

    let customers: Vec<Customer> = sqlx::query_as(
        "SELECT id, name, email, phone, address, date_of_birth, is_active, created_at, updated_at
         FROM customers WHERE is_active = 1 ORDER BY name"
    )
    .fetch_all(pool)
    .await?;

    Ok(customers)
}

#[tauri::command]
pub async fn get_customer_by_id(
    pool: State<'_, DbPool>,
    token: String,
    customer_id: i64,
) -> Result<ApiResponse<Customer>, String> {
    let result = get_customer_by_id_internal(&pool, token, customer_id).await;
    
    match result {
        Ok(customer) => Ok(ApiResponse::success(customer)),
        Err(e) => Ok(e.into()),
    }
}

async fn get_customer_by_id_internal(pool: &DbPool, token: String, customer_id: i64) -> AppResult<Customer> {
    let _user_session = AuthService::extract_user_from_token(&token)?;

    let customer: Customer = sqlx::query_as(
        "SELECT id, name, email, phone, address, date_of_birth, is_active, created_at, updated_at
         FROM customers WHERE id = ?"
    )
    .bind(customer_id)
    .fetch_optional(pool)
    .await?
    .ok_or_else(|| AppError::NotFound("Customer not found".to_string()))?;

    Ok(customer)
}

#[tauri::command]
pub async fn get_customers_with_stats(
    pool: State<'_, DbPool>,
    token: String,
) -> Result<ApiResponse<Vec<CustomerWithStats>>, String> {
    let result = get_customers_with_stats_internal(&pool, token).await;
    
    match result {
        Ok(customers) => Ok(ApiResponse::success(customers)),
        Err(e) => Ok(e.into()),
    }
}

async fn get_customers_with_stats_internal(pool: &DbPool, token: String) -> AppResult<Vec<CustomerWithStats>> {
    let _user_session = AuthService::extract_user_from_token(&token)?;

    let customers: Vec<Customer> = sqlx::query_as(
        "SELECT id, name, email, phone, address, date_of_birth, is_active, created_at, updated_at
         FROM customers WHERE is_active = 1 ORDER BY name"
    )
    .fetch_all(pool)
    .await?;

    // Get transaction stats for each customer
    let stats_rows: Vec<(i64, i64, f64, Option<chrono::DateTime<Utc>>)> = sqlx::query_as(
        "SELECT customer_id, COUNT(*) as total_transactions, 
                COALESCE(SUM(total_amount), 0) as total_spent,
                MAX(transaction_date) as last_transaction_date
         FROM transactions 
         WHERE customer_id IS NOT NULL AND status = 'completed'
         GROUP BY customer_id"
    )
    .fetch_all(pool)
    .await?;

    let stats_map: std::collections::HashMap<i64, (i64, f64, Option<chrono::DateTime<Utc>>)> = 
        stats_rows.into_iter().map(|(id, count, spent, last_date)| (id, (count, spent, last_date))).collect();

    let result: Vec<CustomerWithStats> = customers
        .into_iter()
        .map(|customer| {
            let (total_transactions, total_spent, last_transaction_date) = 
                stats_map.get(&customer.id).unwrap_or(&(0, 0.0, None));
            CustomerWithStats {
                customer,
                total_transactions: *total_transactions,
                total_spent: *total_spent,
                last_transaction_date: *last_transaction_date,
            }
        })
        .collect();

    Ok(result)
}

#[tauri::command]
pub async fn create_customer(
    pool: State<'_, DbPool>,
    token: String,
    request: CreateCustomerRequest,
) -> Result<ApiResponse<Customer>, String> {
    let result = create_customer_internal(&pool, token, request).await;
    
    match result {
        Ok(customer) => Ok(ApiResponse::success(customer)),
        Err(e) => Ok(e.into()),
    }
}

async fn create_customer_internal(pool: &DbPool, token: String, request: CreateCustomerRequest) -> AppResult<Customer> {
    let _user_session = AuthService::extract_user_from_token(&token)?;

    // Check if phone number already exists (if provided)
    if let Some(ref phone) = request.phone {
        if !phone.trim().is_empty() {
            let existing_customer: Option<Customer> = sqlx::query_as(
                "SELECT id, name, email, phone, address, date_of_birth, is_active, created_at, updated_at
                 FROM customers WHERE phone = ? AND is_active = 1"
            )
            .bind(phone.trim())
            .fetch_optional(pool)
            .await?;

            if existing_customer.is_some() {
                return Err(AppError::Conflict("A customer with this phone number already exists".to_string()));
            }
        }
    }

    let now = Utc::now();
    
    let customer_id = sqlx::query(
        "INSERT INTO customers (name, email, phone, address, date_of_birth, is_active, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, 1, ?, ?)"
    )
    .bind(&request.name)
    .bind(&request.email)
    .bind(&request.phone)
    .bind(&request.address)
    .bind(request.date_of_birth)
    .bind(now)
    .bind(now)
    .execute(pool)
    .await?
    .last_insert_rowid();

    let customer: Customer = sqlx::query_as(
        "SELECT id, name, email, phone, address, date_of_birth, is_active, created_at, updated_at
         FROM customers WHERE id = ?"
    )
    .bind(customer_id)
    .fetch_one(pool)
    .await?;

    Ok(customer)
}

#[tauri::command]
pub async fn update_customer(
    pool: State<'_, DbPool>,
    token: String,
    customer_id: i64,
    request: UpdateCustomerRequest,
) -> Result<ApiResponse<Customer>, String> {
    let result = update_customer_internal(&pool, token, customer_id, request).await;
    
    match result {
        Ok(customer) => Ok(ApiResponse::success(customer)),
        Err(e) => Ok(e.into()),
    }
}

async fn update_customer_internal(pool: &DbPool, token: String, customer_id: i64, request: UpdateCustomerRequest) -> AppResult<Customer> {
    let user_session = AuthService::extract_user_from_token(&token)?;
    // Check if user has permission (admin or manager)
    require_manager_or_admin(&user_session)?;

    // Check if customer exists
    let existing_customer: Customer = sqlx::query_as(
        "SELECT id, name, email, phone, address, date_of_birth, is_active, created_at, updated_at
         FROM customers WHERE id = ?"
    )
    .bind(customer_id)
    .fetch_optional(pool)
    .await?
    .ok_or_else(|| AppError::NotFound("Customer not found".to_string()))?;

    // Check if phone number already exists (if provided and different from current)
    if let Some(ref phone) = request.phone {
        if !phone.trim().is_empty() && Some(phone.trim()) != existing_customer.phone.as_deref() {
            let phone_exists: Option<Customer> = sqlx::query_as(
                "SELECT id, name, email, phone, address, date_of_birth, is_active, created_at, updated_at
                 FROM customers WHERE phone = ? AND id != ? AND is_active = 1"
            )
            .bind(phone.trim())
            .bind(customer_id)
            .fetch_optional(pool)
            .await?;

            if phone_exists.is_some() {
                return Err(AppError::Conflict("A customer with this phone number already exists".to_string()));
            }
        }
    }

    let now = Utc::now();
    
    sqlx::query(
        "UPDATE customers 
         SET name = COALESCE(?, name),
             email = COALESCE(?, email),
             phone = COALESCE(?, phone),
             address = COALESCE(?, address),
             date_of_birth = COALESCE(?, date_of_birth),
             is_active = COALESCE(?, is_active),
             updated_at = ?
         WHERE id = ?"
    )
    .bind(&request.name)
    .bind(&request.email)
    .bind(&request.phone)
    .bind(&request.address)
    .bind(request.date_of_birth)
    .bind(request.is_active)
    .bind(now)
    .bind(customer_id)
    .execute(pool)
    .await?;

    let updated_customer: Customer = sqlx::query_as(
        "SELECT id, name, email, phone, address, date_of_birth, is_active, created_at, updated_at
         FROM customers WHERE id = ?"
    )
    .bind(customer_id)
    .fetch_one(pool)
    .await?;

    Ok(updated_customer)
}

#[tauri::command]
pub async fn search_customers_by_phone(
    pool: State<'_, DbPool>,
    token: String,
    phone: String,
) -> Result<ApiResponse<Vec<Customer>>, String> {
    let result = search_customers_by_phone_internal(&pool, token, phone).await;
    
    match result {
        Ok(customers) => Ok(ApiResponse::success(customers)),
        Err(e) => Ok(e.into()),
    }
}

async fn search_customers_by_phone_internal(pool: &DbPool, token: String, phone: String) -> AppResult<Vec<Customer>> {
    let _user_session = AuthService::extract_user_from_token(&token)?;

    let customers: Vec<Customer> = sqlx::query_as(
        "SELECT id, name, email, phone, address, date_of_birth, is_active, created_at, updated_at
         FROM customers WHERE phone LIKE ? AND is_active = 1 ORDER BY name LIMIT 10"
    )
    .bind(format!("%{}%", phone))
    .fetch_all(pool)
    .await?;

    Ok(customers)
}

#[tauri::command]
pub async fn search_customers_by_name(
    pool: State<'_, DbPool>,
    token: String,
    name: String,
) -> Result<ApiResponse<Vec<Customer>>, String> {
    let result = search_customers_by_name_internal(&pool, token, name).await;
    
    match result {
        Ok(customers) => Ok(ApiResponse::success(customers)),
        Err(e) => Ok(e.into()),
    }
}

async fn search_customers_by_name_internal(pool: &DbPool, token: String, name: String) -> AppResult<Vec<Customer>> {
    let _user_session = AuthService::extract_user_from_token(&token)?;

    let customers: Vec<Customer> = sqlx::query_as(
        "SELECT id, name, email, phone, address, date_of_birth, is_active, created_at, updated_at
         FROM customers WHERE name LIKE ? AND is_active = 1 ORDER BY name LIMIT 10"
    )
    .bind(format!("%{}%", name))
    .fetch_all(pool)
    .await?;

    Ok(customers)
}
