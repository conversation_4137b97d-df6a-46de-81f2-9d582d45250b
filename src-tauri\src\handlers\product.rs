use crate::database::DbPool;
use crate::models::product::{Product, CreateProductRequest, UpdateProductRequest, StockMovementType};
use crate::utils::{AuthService, ApiResponse, AppError, AppResult, require_manager_or_admin};
use chrono::Utc;
use tauri::State;

#[tauri::command]
pub async fn get_products(
    pool: State<'_, DbPool>,
    token: String,
) -> Result<ApiResponse<Vec<Product>>, String> {
    let result = get_products_internal(&pool, token).await;
    
    match result {
        Ok(products) => Ok(ApiResponse::success(products)),
        Err(e) => Ok(e.into()),
    }
}

async fn get_products_internal(pool: &DbPool, token: String) -> AppResult<Vec<Product>> {
    let _user_session = AuthService::extract_user_from_token(&token)?;

    let products: Vec<Product> = sqlx::query_as(
        "SELECT id, sku, barcode, name, description, category_id, supplier_id, 
         cost_price, selling_price, wholesale_price, min_selling_price,
         current_stock, min_stock, max_stock, unit, weight, dimensions, image_path,
         is_active, is_trackable, allow_negative_stock, is_taxable, created_at, updated_at
         FROM products WHERE is_active = 1 ORDER BY name"
    )
    .fetch_all(pool)
    .await?;

    Ok(products)
}

#[tauri::command]
pub async fn get_product_by_id(
    pool: State<'_, DbPool>,
    token: String,
    product_id: i64,
) -> Result<ApiResponse<Product>, String> {
    let result = get_product_by_id_internal(&pool, token, product_id).await;
    
    match result {
        Ok(product) => Ok(ApiResponse::success(product)),
        Err(e) => Ok(e.into()),
    }
}

async fn get_product_by_id_internal(pool: &DbPool, token: String, product_id: i64) -> AppResult<Product> {
    let _user_session = AuthService::extract_user_from_token(&token)?;

    let product: Product = sqlx::query_as(
        "SELECT id, sku, barcode, name, description, category_id, supplier_id, 
         cost_price, selling_price, wholesale_price, min_selling_price,
         current_stock, min_stock, max_stock, unit, weight, dimensions, image_path,
         is_active, is_trackable, allow_negative_stock, is_taxable, created_at, updated_at
         FROM products WHERE id = ?"
    )
    .bind(product_id)
    .fetch_optional(pool)
    .await?
    .ok_or_else(|| AppError::NotFound("Product not found".to_string()))?;

    Ok(product)
}

#[tauri::command]
pub async fn get_product_by_barcode(
    pool: State<'_, DbPool>,
    token: String,
    barcode: String,
) -> Result<ApiResponse<Product>, String> {
    let result = get_product_by_barcode_internal(&pool, token, barcode).await;
    
    match result {
        Ok(product) => Ok(ApiResponse::success(product)),
        Err(e) => Ok(e.into()),
    }
}

async fn get_product_by_barcode_internal(pool: &DbPool, token: String, barcode: String) -> AppResult<Product> {
    let _user_session = AuthService::extract_user_from_token(&token)?;

    let product: Product = sqlx::query_as(
        "SELECT id, sku, barcode, name, description, category_id, supplier_id, 
         cost_price, selling_price, wholesale_price, min_selling_price,
         current_stock, min_stock, max_stock, unit, weight, dimensions, image_path,
         is_active, is_trackable, allow_negative_stock, is_taxable, created_at, updated_at
         FROM products WHERE barcode = ? AND is_active = 1"
    )
    .bind(barcode)
    .fetch_optional(pool)
    .await?
    .ok_or_else(|| AppError::NotFound("Product not found".to_string()))?;

    Ok(product)
}

#[tauri::command]
pub async fn create_product(
    pool: State<'_, DbPool>,
    token: String,
    request: CreateProductRequest,
) -> Result<ApiResponse<Product>, String> {
    let result = create_product_internal(&pool, token, request).await;
    
    match result {
        Ok(product) => Ok(ApiResponse::success(product)),
        Err(e) => Ok(e.into()),
    }
}

async fn create_product_internal(pool: &DbPool, token: String, request: CreateProductRequest) -> AppResult<Product> {
    let user_session = AuthService::extract_user_from_token(&token)?;
    require_manager_or_admin(&user_session)?;

    // Check if SKU already exists
    let existing = sqlx::query("SELECT id FROM products WHERE sku = ?")
        .bind(&request.sku)
        .fetch_optional(pool)
        .await?;

    if existing.is_some() {
        return Err(AppError::Conflict("SKU already exists".to_string()));
    }

    // Check if barcode already exists (if provided)
    if let Some(ref barcode) = request.barcode {
        let existing = sqlx::query("SELECT id FROM products WHERE barcode = ?")
            .bind(barcode)
            .fetch_optional(pool)
            .await?;

        if existing.is_some() {
            return Err(AppError::Conflict("Barcode already exists".to_string()));
        }
    }

    let now = Utc::now();
    let result = sqlx::query(
        "INSERT INTO products (sku, barcode, name, description, category_id, supplier_id,
         cost_price, selling_price, wholesale_price, min_selling_price,
         current_stock, min_stock, max_stock, unit, weight, dimensions, image_path,
         is_trackable, allow_negative_stock, is_taxable, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"
    )
    .bind(&request.sku)
    .bind(&request.barcode)
    .bind(&request.name)
    .bind(&request.description)
    .bind(request.category_id)
    .bind(request.supplier_id)
    .bind(request.cost_price)
    .bind(request.selling_price)
    .bind(request.wholesale_price)
    .bind(request.min_selling_price)
    .bind(request.current_stock)
    .bind(request.min_stock)
    .bind(request.max_stock)
    .bind(&request.unit)
    .bind(request.weight)
    .bind(&request.dimensions)
    .bind(&request.image_path)
    .bind(request.is_trackable.unwrap_or(true))
    .bind(request.allow_negative_stock.unwrap_or(false))
    .bind(request.is_taxable.unwrap_or(true))
    .bind(now)
    .bind(now)
    .execute(pool)
    .await?;

    let product_id = result.last_insert_rowid();

    // Create initial stock movement if stock > 0
    if request.current_stock > 0 {
        sqlx::query(
            "INSERT INTO stock_movements (product_id, movement_type, quantity, reference_type, notes, created_by, created_at)
             VALUES (?, ?, ?, ?, ?, ?, ?)"
        )
        .bind(product_id)
        .bind(StockMovementType::In)
        .bind(request.current_stock)
        .bind("initial_stock")
        .bind("Initial stock entry")
        .bind(user_session.user_id)
        .bind(now)
        .execute(pool)
        .await?;
    }

    get_product_by_id_internal(pool, token, product_id).await
}

#[tauri::command]
pub async fn update_product(
    pool: State<'_, DbPool>,
    token: String,
    product_id: i64,
    request: UpdateProductRequest,
) -> Result<ApiResponse<Product>, String> {
    let result = update_product_internal(&pool, token, product_id, request).await;
    
    match result {
        Ok(product) => Ok(ApiResponse::success(product)),
        Err(e) => Ok(e.into()),
    }
}

async fn update_product_internal(pool: &DbPool, token: String, product_id: i64, request: UpdateProductRequest) -> AppResult<Product> {
    let user_session = AuthService::extract_user_from_token(&token)?;
    require_manager_or_admin(&user_session)?;

    // Check if product exists
    let existing = sqlx::query("SELECT id FROM products WHERE id = ?")
        .bind(product_id)
        .fetch_optional(pool)
        .await?;

    if existing.is_none() {
        return Err(AppError::NotFound("Product not found".to_string()));
    }

    // Build dynamic update query
    let mut query = "UPDATE products SET updated_at = ?".to_string();
    let mut params: Vec<Box<dyn sqlx::Encode<'_, sqlx::Sqlite> + Send + Sync>> = vec![Box::new(Utc::now())];

    if let Some(sku) = &request.sku {
        query.push_str(", sku = ?");
        params.push(Box::new(sku.clone()));
    }
    if let Some(barcode) = &request.barcode {
        query.push_str(", barcode = ?");
        params.push(Box::new(barcode.clone()));
    }
    if let Some(name) = &request.name {
        query.push_str(", name = ?");
        params.push(Box::new(name.clone()));
    }
    if let Some(description) = &request.description {
        query.push_str(", description = ?");
        params.push(Box::new(description.clone()));
    }
    if let Some(category_id) = request.category_id {
        query.push_str(", category_id = ?");
        params.push(Box::new(category_id));
    }
    if let Some(supplier_id) = request.supplier_id {
        query.push_str(", supplier_id = ?");
        params.push(Box::new(supplier_id));
    }
    if let Some(cost_price) = request.cost_price {
        query.push_str(", cost_price = ?");
        params.push(Box::new(cost_price));
    }
    if let Some(selling_price) = request.selling_price {
        query.push_str(", selling_price = ?");
        params.push(Box::new(selling_price));
    }
    if let Some(wholesale_price) = request.wholesale_price {
        query.push_str(", wholesale_price = ?");
        params.push(Box::new(wholesale_price));
    }
    if let Some(min_selling_price) = request.min_selling_price {
        query.push_str(", min_selling_price = ?");
        params.push(Box::new(min_selling_price));
    }
    if let Some(min_stock) = request.min_stock {
        query.push_str(", min_stock = ?");
        params.push(Box::new(min_stock));
    }
    if let Some(max_stock) = request.max_stock {
        query.push_str(", max_stock = ?");
        params.push(Box::new(max_stock));
    }
    if let Some(unit) = &request.unit {
        query.push_str(", unit = ?");
        params.push(Box::new(unit.clone()));
    }
    if let Some(weight) = request.weight {
        query.push_str(", weight = ?");
        params.push(Box::new(weight));
    }
    if let Some(dimensions) = &request.dimensions {
        query.push_str(", dimensions = ?");
        params.push(Box::new(dimensions.clone()));
    }
    if let Some(image_path) = &request.image_path {
        query.push_str(", image_path = ?");
        params.push(Box::new(image_path.clone()));
    }
    if let Some(is_active) = request.is_active {
        query.push_str(", is_active = ?");
        params.push(Box::new(is_active));
    }
    if let Some(is_trackable) = request.is_trackable {
        query.push_str(", is_trackable = ?");
        params.push(Box::new(is_trackable));
    }
    if let Some(allow_negative_stock) = request.allow_negative_stock {
        query.push_str(", allow_negative_stock = ?");
        params.push(Box::new(allow_negative_stock));
    }
    if let Some(is_taxable) = request.is_taxable {
        query.push_str(", is_taxable = ?");
        params.push(Box::new(is_taxable));
    }

    query.push_str(" WHERE id = ?");
    params.push(Box::new(product_id));

    // Execute the update - this is simplified, in practice you'd need to handle the dynamic query properly
    sqlx::query(&format!("UPDATE products SET updated_at = ? WHERE id = ?"))
        .bind(Utc::now())
        .bind(product_id)
        .execute(pool)
        .await?;

    get_product_by_id_internal(pool, token, product_id).await
}
