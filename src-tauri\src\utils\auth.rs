use crate::models::user::{User, UserSession, UserRole};
use crate::utils::error::{AppError, AppResult};
use bcrypt::{hash, verify, DEFAULT_COST};
use chrono::{Duration, Utc};
use jsonwebtoken::{decode, encode, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>};
use once_cell::sync::Lazy;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Mutex;

// JWT secret key - in production, this should be loaded from environment variables
const JWT_SECRET: &str = "your-secret-key-change-this-in-production";
const JWT_EXPIRATION_HOURS: i64 = 24;
const REFRESH_TOKEN_EXPIRATION_DAYS: i64 = 30;

// In-memory store for refresh tokens - in production, use Redis or database
static REFRESH_TOKENS: Lazy<Mutex<HashMap<String, RefreshTokenData>>> = 
    Lazy::new(|| Mutex::new(HashMap::new()));

#[derive(Debug, Serialize, Deserialize)]
pub struct Claims {
    pub sub: String, // user_id
    pub username: String,
    pub role: String,
    pub exp: usize,
    pub iat: usize,
}

#[derive(Debug, Clone)]
struct RefreshTokenData {
    user_id: i64,
    expires_at: chrono::DateTime<Utc>,
}

pub struct AuthService;

impl AuthService {
    pub fn hash_password(password: &str) -> AppResult<String> {
        hash(password, DEFAULT_COST).map_err(AppError::from)
    }

    pub fn verify_password(password: &str, hash: &str) -> AppResult<bool> {
        verify(password, hash).map_err(AppError::from)
    }

    pub fn generate_jwt_token(user: &User) -> AppResult<String> {
        let now = Utc::now();
        let exp = now + Duration::hours(JWT_EXPIRATION_HOURS);

        let claims = Claims {
            sub: user.id.to_string(),
            username: user.username.clone(),
            role: user.role.to_string(),
            exp: exp.timestamp() as usize,
            iat: now.timestamp() as usize,
        };

        let header = Header::new(Algorithm::HS256);
        let encoding_key = EncodingKey::from_secret(JWT_SECRET.as_ref());

        encode(&header, &claims, &encoding_key).map_err(AppError::from)
    }

    pub fn generate_refresh_token(user_id: i64) -> String {
        let token = uuid::Uuid::new_v4().to_string();
        let expires_at = Utc::now() + Duration::days(REFRESH_TOKEN_EXPIRATION_DAYS);

        let mut tokens = REFRESH_TOKENS.lock().unwrap();
        tokens.insert(token.clone(), RefreshTokenData {
            user_id,
            expires_at,
        });

        token
    }

    pub fn verify_jwt_token(token: &str) -> AppResult<Claims> {
        let decoding_key = DecodingKey::from_secret(JWT_SECRET.as_ref());
        let validation = Validation::new(Algorithm::HS256);

        let token_data = decode::<Claims>(token, &decoding_key, &validation)?;
        Ok(token_data.claims)
    }

    pub fn verify_refresh_token(token: &str) -> AppResult<i64> {
        let mut tokens = REFRESH_TOKENS.lock().unwrap();
        
        if let Some(token_data) = tokens.get(token) {
            if token_data.expires_at > Utc::now() {
                Ok(token_data.user_id)
            } else {
                tokens.remove(token);
                Err(AppError::Authentication("Refresh token expired".to_string()))
            }
        } else {
            Err(AppError::Authentication("Invalid refresh token".to_string()))
        }
    }

    pub fn revoke_refresh_token(token: &str) {
        let mut tokens = REFRESH_TOKENS.lock().unwrap();
        tokens.remove(token);
    }

    pub fn cleanup_expired_tokens() {
        let mut tokens = REFRESH_TOKENS.lock().unwrap();
        let now = Utc::now();
        tokens.retain(|_, data| data.expires_at > now);
    }

    pub fn extract_user_from_token(token: &str) -> AppResult<UserSession> {
        let claims = Self::verify_jwt_token(token)?;
        
        let user_id = claims.sub.parse::<i64>()
            .map_err(|_| AppError::Authentication("Invalid user ID in token".to_string()))?;

        let role = match claims.role.as_str() {
            "admin" => UserRole::Admin,
            "manager" => UserRole::Manager,
            "cashier" => UserRole::Cashier,
            _ => return Err(AppError::Authentication("Invalid role in token".to_string())),
        };

        Ok(UserSession {
            user_id,
            username: claims.username,
            role,
            exp: claims.exp,
        })
    }
}

// Authorization helpers
pub fn require_role(user_session: &UserSession, required_role: UserRole) -> AppResult<()> {
    match (&user_session.role, &required_role) {
        (UserRole::Admin, _) => Ok(()), // Admin can access everything
        (UserRole::Manager, UserRole::Manager | UserRole::Cashier) => Ok(()),
        (UserRole::Cashier, UserRole::Cashier) => Ok(()),
        _ => Err(AppError::Authorization(
            "Insufficient permissions".to_string()
        )),
    }
}

pub fn require_admin(user_session: &UserSession) -> AppResult<()> {
    require_role(user_session, UserRole::Admin)
}

pub fn require_manager_or_admin(user_session: &UserSession) -> AppResult<()> {
    match user_session.role {
        UserRole::Admin | UserRole::Manager => Ok(()),
        _ => Err(AppError::Authorization(
            "Manager or admin role required".to_string()
        )),
    }
}

// Utility function to generate transaction numbers
pub fn generate_transaction_number() -> String {
    let now = Utc::now();
    let timestamp = now.format("%Y%m%d%H%M%S");
    let random = uuid::Uuid::new_v4().to_string().chars().take(6).collect::<String>();
    format!("TXN{}{}", timestamp, random.to_uppercase())
}
