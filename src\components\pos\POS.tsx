import React, { useState, useEffect } from 'react';
import { Search, ShoppingCart, Plus, X, Edit2, Trash2, Barcode } from 'lucide-react';
import { toast } from 'sonner';
import { useQuery, useQueryClient } from '@tanstack/react-query';

import { usePOSStore } from '@/stores/posStore';

import { formatCurrency } from '@/lib/utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/common/Card';
import Input from '@/components/common/Input';
import Button from '@/components/common/Button';
import ProductGrid from './ProductGrid';
import Cart from './Cart';
import PaymentModal from './PaymentModal';
import ReceiptModal from './ReceiptModal';
import CategoryFilter from './CategoryFilter';
import { productService } from '@/services/productService';
import { categoryService } from '@/services/categoryService';
import Loading from '@/components/common/Loading';



const POS: React.FC = () => {
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(null);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showReceiptModal, setShowReceiptModal] = useState(false);
  const [editingCartName, setEditingCartName] = useState<string | null>(null);
  const [newCartName, setNewCartName] = useState('');
  const [barcodeInput, setBarcodeInput] = useState('');

  // Fetch products from backend
  const { data: products = [], isLoading, error } = useQuery({
    queryKey: ['products'],
    queryFn: productService.getProducts,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Fetch categories from backend
  const { data: categories = [], isLoading: categoriesLoading } = useQuery({
    queryKey: ['categories'],
    queryFn: categoryService.getCategories,
    staleTime: 10 * 60 * 1000, // 10 minutes (categories change less frequently)
  });

  const {
    cart,
    currentTransaction,
    carts,
    activeCartId,
    pendingTransactions,
    discountPercentage,
    discountAmount,
    discountMode,
    notes,
    addToCart,
    createNewCart,
    switchToCart,
    deleteCart,
    renameCart,

    getCartSummary,
    clearCart,
    clearCurrentTransaction,
    setDiscount,
    setDiscountAmount,
    setDiscountMode,
    setNotes
  } = usePOSStore();

  // Initialize first cart as active if none is selected
  useEffect(() => {
    if (!activeCartId && carts.length > 0) {
      switchToCart(carts[0].id);
    }
  }, [activeCartId, carts, switchToCart]);

  // Debug: Log when currentTransaction changes
  useEffect(() => {
    console.log('currentTransaction changed:', currentTransaction);
  }, [currentTransaction]);

  // Debug: Log when showReceiptModal changes
  useEffect(() => {
    console.log('showReceiptModal changed:', showReceiptModal, 'currentTransaction:', !!currentTransaction);
  }, [showReceiptModal, currentTransaction]);



  // Show loading state
  if (isLoading) {
    return <Loading />;
  }

  // Show error state
  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error loading products</h3>
          <p className="text-gray-600">Please try refreshing the page</p>
        </div>
      </div>
    );
  }

  const filteredProducts = products.filter(product => {
    // Text search filter
    const matchesSearch = searchTerm === '' ||
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.barcode?.includes(searchTerm);

    // Category filter
    const matchesCategory = selectedCategoryId === null ||
      product.categoryId === selectedCategoryId;

    return matchesSearch && matchesCategory;
  });

  const cartSummary = getCartSummary();

  const handleCheckout = () => {
    if (cart.length === 0) {
      toast.error('Cart is empty');
      return;
    }
    setShowPaymentModal(true);
  };

  const handlePaymentSuccess = () => {
    console.log('handlePaymentSuccess called');
    setShowPaymentModal(false);
    setShowReceiptModal(true);
    toast.success('Transaction completed successfully!');

    // Invalidate products cache to refresh stock information
    queryClient.invalidateQueries({ queryKey: ['products'] });
  };

  const handlePendingPayment = () => {
    setShowPaymentModal(false);
    toast.success('Transaction saved as pending payment');
  };

  const handleCreateNewCart = () => {
    createNewCart();
    toast.success('New cart created');
  };

  const handleSwitchCart = (cartId: string) => {
    switchToCart(cartId);
    toast.success('Switched to cart');
  };

  const handleDeleteCart = (cartId: string) => {
    if (carts.length <= 1) {
      toast.error('Cannot delete the last cart');
      return;
    }
    deleteCart(cartId);
    toast.success('Cart deleted');
  };

  const handleClearCart = () => {
    if (cart.length === 0) return;

    clearCart();
    toast.success('Cart cleared');
  };

  const handleDiscountChange = (value: string) => {
    if (discountMode === 'percentage') {
      const percentage = parseFloat(value) || 0;
      setDiscount(percentage);
    } else {
      const amount = parseFloat(value) || 0;
      setDiscountAmount(amount);
    }
  };

  const handleRenameCart = (cartId: string, name: string) => {
    renameCart(cartId, name);
    setEditingCartName(null);
    setNewCartName('');
    toast.success('Cart renamed');
  };

  const startEditingCartName = (cartId: string, currentName: string) => {
    setEditingCartName(cartId);
    setNewCartName(currentName);
  };

  const handleBarcodeSubmit = async () => {
    const code = barcodeInput.trim();
    if (!code) return;

    try {
      const product = await productService.getProductByBarcode(code);
      if (product.currentStock === 0 && !product.allowNegativeStock) {
        toast.error('Product is out of stock');
      } else {
        addToCart(product, 1);
        toast.success(`${product.name} added to cart`);
      }
    } catch (error) {
      toast.error('Product not found for barcode');
    }
    setBarcodeInput('');
  };

  return (
    <div className="h-full flex flex-col xl:flex-row gap-4 md:gap-6">
      {/* Left Panel - Products */}
      <div className="flex-1 flex flex-col min-h-0">
        <div className="mb-4">
          <h1 className="text-xl md:text-2xl font-bold text-gray-900 mb-4">Point of Sale</h1>

          {/* Cart Management */}
          <div className="mt-4 mb-4">
            <div className="flex flex-wrap gap-2">
              {carts.map((cart) => (
                <div
                  key={cart.id}
                  className={`relative group flex items-center gap-2 px-3 py-2 rounded-lg border-2 transition-colors ${
                    activeCartId === cart.id
                      ? 'border-primary-500 bg-primary-50 text-primary-700'
                      : 'border-gray-200 bg-white text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {editingCartName === cart.id ? (
                    <div className="flex items-center gap-1">
                      <Input
                        type="text"
                        value={newCartName}
                        onChange={(e) => setNewCartName(e.target.value)}
                        className="h-6 text-xs w-24"
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            handleRenameCart(cart.id, newCartName);
                          } else if (e.key === 'Escape') {
                            setEditingCartName(null);
                            setNewCartName('');
                          }
                        }}
                        autoFocus
                      />
                    </div>
                  ) : (
                    <>
                      <button
                        onClick={() => handleSwitchCart(cart.id)}
                        className="flex items-center gap-2 text-sm font-medium"
                      >
                        {cart.name}
                        <span className="text-xs opacity-70">
                          ({cart.items.length} items)
                        </span>
                      </button>
                      <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                        <button
                          onClick={() => startEditingCartName(cart.id, cart.name)}
                          className="p-1 hover:bg-gray-200 rounded"
                        >
                          <Edit2 className="h-3 w-3" />
                        </button>
                        {carts.length > 1 && (
                          <button
                            onClick={() => handleDeleteCart(cart.id)}
                            className="p-1 hover:bg-red-200 rounded text-red-600"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        )}
                      </div>
                    </>
                  )}
                </div>
              ))}
            <Button
              size="sm"
              onClick={handleCreateNewCart}
              className="h-8"
            >
              <Plus className="h-4 w-4 mr-1" />
              New Cart
            </Button>
            </div>
          </div>

          {/* Search */}
          <div className="flex flex-col justify-between md:flex-row gap-3">
            <Input
              type="text"
              placeholder="Search products..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              leftIcon={<Search className="h-5 w-5" />}
            />
            <Input
              type="text"
              placeholder="Scan or enter barcode..."
              value={barcodeInput}
              onChange={(e) => setBarcodeInput(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleBarcodeSubmit();
                }
              }}
              leftIcon={<Barcode className="h-5 w-5" />}
              rightIcon={
                <button
                  type="button"
                  onClick={handleBarcodeSubmit}
                  className="text-secondary-500 hover:text-secondary-700"
                >
                  Add
                </button>
              }
            />
          </div>

          {/* Category Filter */}
          <CategoryFilter
            categories={categories}
            selectedCategoryId={selectedCategoryId}
            onCategorySelect={setSelectedCategoryId}
            isLoading={categoriesLoading}
          />
        </div>

        {/* Products Grid */}
        <div className="flex-1 overflow-y-auto min-h-0 max-h-[720px]">
          <ProductGrid products={filteredProducts} />
        </div>
      </div>

      {/* Right Panel - Cart */}
      <div className="w-full xl:w-[480px] flex flex-col">
        <Card className="flex-1 flex flex-col max-h-[920px] md:sticky md:top-0">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center text-lg">
                <ShoppingCart className="h-5 w-5 mr-2" />
                Cart ({cartSummary.itemCount} items)
              </CardTitle>
              <Button
                variant="outline"
                size="sm"
                onClick={handleClearCart}
                className="text-red-600 border-red-300 hover:bg-red-50"
              >
                <Trash2 className="h-4 w-4 mr-1" />
                Clear
              </Button>
            </div>
          </CardHeader>
          <CardContent className="flex-1 flex flex-col p-0 min-h-0">
            <div className="flex-1 overflow-y-auto px-4 md:px-6 max-h-[450px]">
              <Cart />
            </div>

            {/* Cart Summary */}
            <div className="border-t p-4 md:p-6 space-y-3 bg-gray-50">
              {/* Discount Input */}
              <div className="pt-4 border-t">
                <Input
                  type="number"
                  label="Discount"
                  placeholder="0"
                  value={
                    discountMode === 'percentage'
                      ? discountPercentage.toString()
                      : discountAmount.toString()
                  }
                  onChange={(e) => handleDiscountChange(e.target.value)}
                  rightIcon={
                    <button
                      type="button"
                      onClick={() => setDiscountMode(discountMode === 'percentage' ? 'amount' : 'percentage')}
                      className="text-xs font-medium px-2 py-1 bg-secondary-100 text-secondary-700 rounded"
                      title={discountMode === 'percentage' ? 'Switch to Rupiah' : 'Switch to Percentage'}
                    >
                      {discountMode === 'percentage' ? 'Rp' : '%'}
                    </button>
                  }
                  min="0"
                  max={discountMode === 'percentage' ? '100' : undefined}
                  step={discountMode === 'percentage' ? '0.1' : '100'}
                />
              </div>

              {/* Notes Input */}
              <div>
                <Input
                  type="text"
                  label="Notes (optional)"
                  placeholder="Add transaction notes..."
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                />
              </div>

              {/* Clear Cart Button moved to header */}
              <div className="flex justify-between text-sm">
                <span>Subtotal:</span>
                <span>{formatCurrency(cartSummary.subtotal)}</span>
              </div>
              {cartSummary.discountAmount > 0 && (
                <div className="flex justify-between text-sm text-green-600">
                  <span>Discount:</span>
                  <span>-{formatCurrency(cartSummary.discountAmount)}</span>
                </div>
              )}
              <div className="flex justify-between text-sm">
                <span>Tax:</span>
                <span>{formatCurrency(cartSummary.taxAmount)}</span>
              </div>
              <div className="flex justify-between text-lg font-bold border-t pt-3">
                <span>Total:</span>
                <span>{formatCurrency(cartSummary.totalAmount)}</span>
              </div>

              <Button
                onClick={handleCheckout}
                className="w-full"
                disabled={cart.length === 0}
                size="lg"
              >
                Checkout
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Pending Transactions */}
        {pendingTransactions.length > 0 && (
          <Card className="mt-4">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm text-orange-600">
                Pending Payments ({pendingTransactions.length})
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {pendingTransactions.map((transaction) => (
                <div
                  key={transaction.id}
                  className="flex items-center justify-between p-3 bg-orange-50 rounded-lg border border-orange-200"
                >
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-900">
                        {transaction.transactionNumber}
                      </span>
                      <span className="text-sm font-bold text-orange-600">
                        {formatCurrency(transaction.totalAmount)}
                      </span>
                    </div>
                    {transaction.customerName && (
                      <p className="text-xs text-gray-600 mt-1">
                        {transaction.customerName}
                      </p>
                    )}
                  </div>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      // TODO: Implement complete pending payment modal
                      toast.info('Complete payment feature coming soon');
                    }}
                    className="ml-2 text-orange-600 border-orange-300 hover:bg-orange-100"
                  >
                    Pay Now
                  </Button>
                </div>
              ))}
            </CardContent>
          </Card>
        )}
      </div>

      {/* Modals */}
      {showPaymentModal && (
        <PaymentModal
          isOpen={showPaymentModal}
          onClose={() => setShowPaymentModal(false)}
          onSuccess={handlePaymentSuccess}
          onPendingPayment={handlePendingPayment}
          totalAmount={cartSummary.totalAmount}
        />
      )}

      {/* Receipt Modal */}
      {showReceiptModal && currentTransaction ? (
        <ReceiptModal
          isOpen={showReceiptModal}
          onClose={() => {
            setShowReceiptModal(false);
            clearCurrentTransaction();
          }}
          transaction={currentTransaction}
        />
      ) : showReceiptModal ? (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-md">
            <CardContent className="p-6">
              <p>Loading receipt...</p>
            </CardContent>
          </Card>
        </div>
      ) : null}
    </div>
  );
};

export default POS;
