import React, { useRef } from 'react';
import { X, Printer, Download } from 'lucide-react';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';

import { Transaction } from '@/stores/posStore';
import { formatCurrency, formatDate } from '@/lib/utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/common/Card';
import Button from '@/components/common/Button';

interface ReceiptModalProps {
  isOpen: boolean;
  onClose: () => void;
  transaction: Transaction;
}

const ReceiptModal: React.FC<ReceiptModalProps> = ({
  isOpen,
  onClose,
  transaction,
}) => {
  const receiptRef = useRef<HTMLDivElement>(null);

  const handlePrint = () => {
    window.print();
  };

  const handleDownloadPDF = async () => {
    if (!receiptRef.current) return;

    try {
      const canvas = await html2canvas(receiptRef.current, {
        scale: 2,
        useCORS: true,
        backgroundColor: '#ffffff',
      });

      const imgData = canvas.toDataURL('image/png');
      const pdf = new jsPDF('p', 'mm', 'a4');
      
      const imgWidth = 210;
      const pageHeight = 295;
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      let heightLeft = imgHeight;

      let position = 0;

      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;

      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }

      pdf.save(`receipt-${transaction.transactionNumber}.pdf`);
    } catch (error) {
      console.error('Error generating PDF:', error);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-md max-h-[90vh] overflow-y-auto">
        <CardHeader className="no-print">
          <div className="flex items-center justify-between">
            <CardTitle>Receipt</CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-10 w-10 p-0"
            >
              <X className="h-8 w-8" />
            </Button>
          </div>
        </CardHeader>
        
        <CardContent>
          {/* Receipt Content */}
          <div ref={receiptRef} className="bg-white p-6 text-sm">
            {/* Store Header */}
            <div className="text-center mb-6">
              <h1 className="text-xl font-bold">D-Kasir</h1>
              <p className="text-gray-600">Point of Sale System</p>
              <p className="text-xs text-gray-500 mt-2">
                Jl. Contoh No. 123, Jakarta<br />
                Tel: (021) 1234-5678
              </p>
            </div>

            {/* Transaction Info */}
            <div className="border-t border-b border-gray-300 py-3 mb-4">
              <div className="flex justify-between mb-1">
                <span>Transaction:</span>
                <span className="font-mono">{transaction.transactionNumber}</span>
              </div>
              <div className="flex justify-between mb-1">
                <span>Date:</span>
                <span>{formatDate(transaction.createdAt, 'long')}</span>
              </div>
              <div className="flex justify-between mb-1">
                <span>Time:</span>
                <span>{formatDate(transaction.createdAt, 'time')}</span>
              </div>
              {transaction.customerName && (
                <div className="flex justify-between">
                  <span>Customer:</span>
                  <span>{transaction.customerName}</span>
                </div>
              )}
            </div>

            {/* Items */}
            <div className="mb-4">
              {transaction.items.map((item, index) => (
                <div key={index} className="mb-2">
                  <div className="flex justify-between">
                    <span className="font-medium">{item.product.name}</span>
                    <span>{formatCurrency(item.totalPrice)}</span>
                  </div>
                  <div className="text-xs text-gray-600 ml-2">
                    {formatCurrency(item.unitPrice)} × {item.quantity} {item.product.unit}
                  </div>
                </div>
              ))}
            </div>

            {/* Totals */}
            <div className="border-t border-gray-300 pt-3 space-y-1">
              <div className="flex justify-between">
                <span>Subtotal:</span>
                <span>{formatCurrency(transaction.subtotal)}</span>
              </div>
              
              {transaction.discountAmount > 0 && (
                <div className="flex justify-between text-green-600">
                  <span>
                    {transaction.discountMode === 'percentage'
                      ? `Discount (${transaction.discountPercentage}%)`
                      : 'Discount'}
                    :
                  </span>
                  <span>-{formatCurrency(transaction.discountAmount)}</span>
                </div>
              )}
              
              <div className="flex justify-between">
                <span>Tax ({transaction.taxPercentage}%):</span>
                <span>{formatCurrency(transaction.taxAmount)}</span>
              </div>
              
              <div className="flex justify-between font-bold text-lg border-t border-gray-300 pt-2">
                <span>Total:</span>
                <span>{formatCurrency(transaction.totalAmount)}</span>
              </div>
            </div>

            {/* Payment Info */}
            <div className="border-t border-gray-300 pt-3 mt-3 space-y-1">
              <div className="flex justify-between">
                <span>Payment Method:</span>
                <span>{transaction.paymentMethod.name}</span>
              </div>
              
              <div className="flex justify-between">
                <span>Paid:</span>
                <span>{formatCurrency(transaction.paidAmount)}</span>
              </div>
              
              {transaction.changeAmount > 0 && (
                <div className="flex justify-between font-bold">
                  <span>Change:</span>
                  <span>{formatCurrency(transaction.changeAmount)}</span>
                </div>
              )}
            </div>

            {/* Notes */}
            {transaction.notes && (
              <div className="border-t border-gray-300 pt-3 mt-3">
                <p className="text-xs text-gray-600">
                  <strong>Notes:</strong> {transaction.notes}
                </p>
              </div>
            )}

            {/* Footer */}
            <div className="text-center mt-6 pt-3 border-t border-gray-300">
              <p className="text-xs text-gray-500">
                Thank you for your purchase!<br />
                Please keep this receipt for your records.
              </p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-3 mt-6 no-print">
            <Button
              variant="outline"
              onClick={handlePrint}
              className="flex-1"
            >
              <Printer className="h-4 w-4 mr-2" />
              Print
            </Button>
            <Button
              variant="outline"
              onClick={handleDownloadPDF}
              className="flex-1"
            >
              <Download className="h-4 w-4 mr-2" />
              Download PDF
            </Button>
          </div>

          <div className="mt-3 no-print">
            <Button
              onClick={onClose}
              className="w-full"
            >
              Close
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ReceiptModal;
