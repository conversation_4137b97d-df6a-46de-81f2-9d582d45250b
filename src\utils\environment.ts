/**
 * Environment detection utilities for Tauri vs Browser environments
 */

// Extend the Window interface to include __TAURI__
declare global {
  interface Window {
    __TAURI__?: any;
  }
}

// Check if we're running in a Tauri environment
export const isTauriEnvironment = (): boolean => {
  if (typeof window === 'undefined') return false;

  // Tauri v2 uses __TAURI_INTERNALS__ with an invoke function
  const v2 = (window as any).__TAURI_INTERNALS__;
  if (v2 && typeof v2.invoke === 'function') return true;

  // Backward compatibility (some setups may expose __TAURI__)
  const v1 = (window as any).__TAURI__;
  if (v1 && typeof v1.core?.invoke === 'function') return true;

  // Heuristic fallback: some builds add 'Tauri' to the userAgent
  try {
    if (typeof navigator !== 'undefined' && /Tauri/i.test(navigator.userAgent)) {
      return true;
    }
  } catch (_) {}

  return false;
};

// Check if we're running in a browser environment
export const isBrowserEnvironment = (): boolean => {
  return typeof window !== 'undefined' &&
    !isTauriEnvironment();
};

// Get the current environment type
export type Environment = 'tauri' | 'browser' | 'unknown';

export const getCurrentEnvironment = (): Environment => {
  if (typeof window === 'undefined') {
    return 'unknown';
  }

  // More robust Tauri detection
  if (isTauriEnvironment()) {
    return 'tauri';
  }

  return 'browser';
};

// Wait for Tauri to be available (useful for early app initialization)
export const waitForTauri = (timeout: number = 5000): Promise<boolean> => {
  return new Promise((resolve) => {
    if (isTauriEnvironment()) {
      resolve(true);
      return;
    }

    const startTime = Date.now();
    const checkInterval = setInterval(() => {
      if (isTauriEnvironment()) {
        clearInterval(checkInterval);
        resolve(true);
      } else if (Date.now() - startTime > timeout) {
        clearInterval(checkInterval);
        resolve(false);
      }
    }, 100);
  });
};

// Environment-specific configuration
export const getEnvironmentConfig = () => {
  const env = getCurrentEnvironment();

  return {
    environment: env,
    isTauri: env === 'tauri',
    isBrowser: env === 'browser',
    apiBaseUrl: env === 'browser' ? null : null, // No external API needed
    features: {
      // Tauri features (full functionality with real database)
      authentication: env === 'tauri',
      productManagement: env === 'tauri',
      inventoryManagement: env === 'tauri',
      transactions: env === 'tauri',
      realDatabase: env === 'tauri',
      // Browser features (mock data for testing)
      demoMode: env === 'browser',
      mockData: env === 'browser',
    }
  };
};

// Log environment information
export const logEnvironmentInfo = () => {
  const config = getEnvironmentConfig();
  console.log('Environment Detection:', {
    environment: config.environment,
    isTauri: config.isTauri,
    isBrowser: config.isBrowser,
    features: config.features,
    userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'N/A',
    tauriAvailable: typeof window !== 'undefined'
      ? !!((window as any).__TAURI_INTERNALS__ || (window as any).__TAURI__)
      : false,
  });
};
