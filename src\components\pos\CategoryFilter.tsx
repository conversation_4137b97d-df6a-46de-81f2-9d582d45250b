import React from 'react';
import { Grid, Package, Home, Coffee, Shirt, Filter } from 'lucide-react';
import { Category } from '@/types';
import Button from '@/components/common/Button';

interface CategoryFilterProps {
  categories: Category[];
  selectedCategoryId: number | null;
  onCategorySelect: (categoryId: number | null) => void;
  isLoading?: boolean;
}

// Icon mapping for categories
const getCategoryIcon = (categoryName: string) => {
  const name = categoryName.toLowerCase();
  if (name.includes('electronic')) return Grid;
  if (name.includes('home') || name.includes('garden')) return Home;
  if (name.includes('food') || name.includes('beverage') || name.includes('coffee')) return Coffee;
  if (name.includes('clothing') || name.includes('fashion')) return Shirt;
  return Package; // Default icon
};

const CategoryFilter: React.FC<CategoryFilterProps> = ({
  categories,
  selectedCategoryId,
  onCategorySelect,
  isLoading = false,
}) => {
  if (isLoading) {
    return (
      <div className="flex items-center gap-2 p-2 bg-gray-50 rounded-lg">
        <Filter className="h-4 w-4 text-gray-400" />
        <span className="text-sm text-gray-500">Loading categories...</span>
      </div>
    );
  }

  if (categories.length === 0) {
    return null;
  }

  return (
    <div className="flex flex-wrap items-center gap-2 p-2 bg-gray-50 rounded-lg">
      <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
        <Filter className="h-4 w-4" />
        <span>Filter by Category:</span>
      </div>
      
      {/* All Categories Button */}
      <Button
        variant={selectedCategoryId === null ? 'primary' : 'outline'}
        size="sm"
        onClick={() => onCategorySelect(null)}
        className="flex items-center gap-1.5"
      >
        <Package className="h-3.5 w-3.5" />
        All Products
      </Button>

      {/* Category Buttons */}
      {categories
        .filter(category => category.isActive)
        .sort((a, b) => a.sortOrder - b.sortOrder)
        .map((category) => {
          const IconComponent = getCategoryIcon(category.name);
          const isSelected = selectedCategoryId === category.id;
          
          return (
            <Button
              key={category.id}
              variant={isSelected ? 'primary' : 'outline'}
              size="sm"
              onClick={() => onCategorySelect(category.id)}
              className="flex items-center gap-1.5"
              title={category.description || category.name}
            >
              <IconComponent className="h-3.5 w-3.5" />
              {category.name}
            </Button>
          );
        })}
    </div>
  );
};

export default CategoryFilter;
