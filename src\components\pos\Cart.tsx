import React from 'react';
import { Minus, Plus, Trash2, ShoppingCart, Package } from 'lucide-react';
import { toast } from 'sonner';

import { usePOSStore } from '@/stores/posStore';
import { formatCurrency } from '@/lib/utils';
import Button from '@/components/common/Button';
import { Card, CardContent } from '@/components/common/Card';

const Cart: React.FC = () => {
  const {
    cart,
    updateQuantity,
    removeFromCart
  } = usePOSStore();

  const handleQuantityChange = (productId: number, newQuantity: number) => {
    if (newQuantity < 1) {
      removeFromCart(productId);
      return;
    }

    // Find the cart item and product
    const cartItem = cart.find(item => item.product.id === productId);
    if (!cartItem) return;

    const product = cartItem.product;
    const currentQuantity = cartItem.quantity;
    const quantityDifference = newQuantity - currentQuantity;

    // Check if we're increasing quantity and if there's enough stock
    if (quantityDifference > 0) {
      const availableStock = product.currentStock - currentQuantity;
      if (availableStock <= 0 && !product.allowNegativeStock) {
        toast.error(`Cannot add more. Only ${availableStock} ${product.unit} available in stock`);
        return;
      }
      if (quantityDifference > availableStock && !product.allowNegativeStock) {
        toast.error(`Cannot add ${quantityDifference} ${product.unit}. Only ${availableStock} available in stock`);
        return;
      }
    }

    updateQuantity(productId, newQuantity);
  };

  const handleRemoveItem = (productId: number, productName: string) => {
    removeFromCart(productId);
    toast.success(`${productName} removed from cart`);
  };

  if (cart.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-gray-500">
        <ShoppingCart className="h-16 w-16 mb-4" />
        <h3 className="text-lg font-medium mb-2">Cart is empty</h3>
        <p className="text-sm text-center">Add products to start a transaction</p>
      </div>
    );
  }

  // Sort cart items by addedAt (last added first)
  const sortedCart = [...cart].sort((a, b) =>
    new Date(b.addedAt).getTime() - new Date(a.addedAt).getTime()
  );

  return (
    <div className="space-y-3">
      {/* Cart Items */}
      <div className="space-y-2">
        {sortedCart.map((item) => {
          const { product, quantity, totalPrice, unitPrice } = item;

          return (
            <Card key={product.id} className="shadow-soft">
              <CardContent className="p-0">
                <div className="grid grid-cols-[60px,1fr] gap-3">
                  <div className="flex flex-col justify-between">
                    <div className="w-full aspect-square rounded-md bg-gray-100 flex items-center justify-center overflow-hidden">
                      {product.imagePath ? (
                        <img
                          src={product.imagePath}
                          alt={product.name}
                          className="h-full w-full object-cover"
                        />
                      ) : (
                        <Package className="h-64 w-6 text-gray-400" />
                      )}
                    </div>
                    <p className="mt-2 text-xs font-semibold text-gray-900">
                      {formatCurrency(totalPrice)}
                    </p>
                  </div>

                  <div className="flex flex-col justify-between gap-3">
                    <div className="flex items-start justify-between gap-2">
                      <div className="min-w-0 flex-1">
                        <h4 className="text-xs font-semibold text-gray-900 line-clamp-1">
                          {product.name}
                        </h4>
                        <p className="mt-1 text-xs font-medium text-gray-900">
                          {formatCurrency(unitPrice)}
                          <span className="ml-1 text-xs text-gray-500">
                            x {quantity} {product.unit}
                          </span>
                        </p>
                      </div>

                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handleRemoveItem(product.id, product.name)}
                        className="h-10 w-10 p-0 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg flex-shrink-0"
                        aria-label={`Remove ${product.name}`}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>

                    <div className="flex items-center justify-end gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleQuantityChange(product.id, quantity - 1)}
                        className="h-9 w-9 p-0 border hover:bg-red-50 hover:border-red-300"
                        aria-label={`Decrease quantity of ${product.name}`}
                      >
                        <Minus className="h-3 w-3" />
                      </Button>

                      <div className="min-w-[2.5rem] rounded border border-gray-300 bg-white px-2 py-1 text-center text-sm font-medium text-gray-900">
                        {quantity}
                      </div>

                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleQuantityChange(product.id, quantity + 1)}
                        className="h-9 w-9 p-0 border hover:bg-green-50 hover:border-green-300"
                        aria-label={`Increase quantity of ${product.name}`}
                      >
                        <Plus className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
};

export default Cart;
