use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use chrono::{DateTime, Utc};

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Product {
    pub id: i64,
    pub sku: String,
    pub barcode: Option<String>,
    pub name: String,
    pub description: Option<String>,
    // #[serde(rename = "categoryId")]
    pub category_id: Option<i64>,
    // #[serde(rename = "supplierId")]
    pub supplier_id: Option<i64>,
    // #[serde(rename = "costPrice")]
    pub cost_price: f64,
    // #[serde(rename = "sellingPrice")]
    pub selling_price: f64,
    // #[serde(rename = "wholesalePrice")]
    pub wholesale_price: Option<f64>,
    // #[serde(rename = "minSellingPrice")]
    pub min_selling_price: Option<f64>,
    // #[serde(rename = "currentStock")]
    pub current_stock: i64,
    // #[serde(rename = "minStock")]
    pub min_stock: i64,
    // #[serde(rename = "maxStock")]
    pub max_stock: Option<i64>,
    pub unit: String,
    pub weight: Option<f64>,
    pub dimensions: Option<String>,
    // #[serde(rename = "imagePath")]
    pub image_path: Option<String>,
    // #[serde(rename = "isActive")]
    pub is_active: bool,
    // #[serde(rename = "isTrackable")]
    pub is_trackable: bool,
    // #[serde(rename = "allowNegativeStock")]
    pub allow_negative_stock: bool,
    // #[serde(rename = "isTaxable")]
    pub is_taxable: bool,
    // #[serde(rename = "createdAt")]
    pub created_at: DateTime<Utc>,
    // #[serde(rename = "updatedAt")]
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Deserialize)]
pub struct CreateProductRequest {
    pub sku: String,
    pub barcode: Option<String>,
    pub name: String,
    pub description: Option<String>,
    // #[serde(rename = "categoryId")]
    pub category_id: Option<i64>,
    // #[serde(rename = "supplierId")]
    pub supplier_id: Option<i64>,
    // #[serde(rename = "costPrice")]
    pub cost_price: f64,
    // #[serde(rename = "sellingPrice")]
    pub selling_price: f64,
    // #[serde(rename = "wholesalePrice")]
    pub wholesale_price: Option<f64>,
    // #[serde(rename = "minSellingPrice")]
    pub min_selling_price: Option<f64>,
    // #[serde(rename = "currentStock")]
    pub current_stock: i64,
    // #[serde(rename = "minStock")]
    pub min_stock: i64,
    // #[serde(rename = "maxStock")]
    pub max_stock: Option<i64>,
    pub unit: String,
    pub weight: Option<f64>,
    pub dimensions: Option<String>,
    // #[serde(rename = "imagePath")]
    pub image_path: Option<String>,
    // #[serde(rename = "isTrackable")]
    pub is_trackable: Option<bool>,
    // #[serde(rename = "allowNegativeStock")]
    pub allow_negative_stock: Option<bool>,
    // #[serde(rename = "isTaxable")]
    pub is_taxable: Option<bool>,
}

#[derive(Debug, Deserialize)]
pub struct UpdateProductRequest {
    pub sku: Option<String>,
    pub barcode: Option<String>,
    pub name: Option<String>,
    pub description: Option<String>,
    // #[serde(rename = "categoryId")]
    pub category_id: Option<i64>,
    // #[serde(rename = "supplierId")]
    pub supplier_id: Option<i64>,
    // #[serde(rename = "costPrice")]
    pub cost_price: Option<f64>,
    // #[serde(rename = "sellingPrice")]
    pub selling_price: Option<f64>,
    // #[serde(rename = "wholesalePrice")]
    pub wholesale_price: Option<f64>,
    // #[serde(rename = "minSellingPrice")]
    pub min_selling_price: Option<f64>,
    // #[serde(rename = "minStock")]
    pub min_stock: Option<i64>,
    // #[serde(rename = "maxStock")]
    pub max_stock: Option<i64>,
    pub unit: Option<String>,
    pub weight: Option<f64>,
    pub dimensions: Option<String>,
    // #[serde(rename = "imagePath")]
    pub image_path: Option<String>,
    // #[serde(rename = "isActive")]
    pub is_active: Option<bool>,
    // #[serde(rename = "isTrackable")]
    pub is_trackable: Option<bool>,
    // #[serde(rename = "allowNegativeStock")]
    pub allow_negative_stock: Option<bool>,
    // #[serde(rename = "isTaxable")]
    pub is_taxable: Option<bool>,
}

#[derive(Debug, Deserialize)]
pub struct StockAdjustmentRequest {
    // #[serde(rename = "productId")]
    pub product_id: i64,
    pub quantity: f64,
    // #[serde(rename = "movementType")]
    pub movement_type: StockMovementType,
    pub notes: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct StockMovement {
    pub id: i64,
    // #[serde(rename = "productId")]
    pub product_id: i64,
    // #[serde(rename = "movementType")]
    pub movement_type: StockMovementType,
    pub quantity: f64,
    // #[serde(rename = "referenceType")]
    pub reference_type: Option<String>,
    // #[serde(rename = "referenceId")]
    pub reference_id: Option<i64>,
    pub notes: Option<String>,
    // #[serde(rename = "createdBy")]
    pub created_by: i64,
    // #[serde(rename = "createdAt")]
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "TEXT")]
pub enum StockMovementType {
    #[sqlx(rename = "in")]
    In,
    #[sqlx(rename = "out")]
    Out,
    #[sqlx(rename = "adjustment")]
    Adjustment,
}

impl std::fmt::Display for StockMovementType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            StockMovementType::In => write!(f, "in"),
            StockMovementType::Out => write!(f, "out"),
            StockMovementType::Adjustment => write!(f, "adjustment"),
        }
    }
}

#[derive(Debug, Serialize)]
pub struct ProductWithStock {
    #[serde(flatten)]
    pub product: Product,
    pub stock_status: StockStatus,
}

#[derive(Debug, Serialize)]
pub enum StockStatus {
    InStock,
    LowStock,
    OutOfStock,
}

impl Product {
    pub fn get_stock_status(&self) -> StockStatus {
        if self.current_stock <= 0 {
            StockStatus::OutOfStock
        } else if self.current_stock <= self.min_stock {
            StockStatus::LowStock
        } else {
            StockStatus::InStock
        }
    }

    pub fn can_sell(&self, quantity: i64) -> bool {
        if !self.is_active {
            return false;
        }
        
        if self.allow_negative_stock {
            return true;
        }
        
        self.current_stock >= quantity
    }
}
