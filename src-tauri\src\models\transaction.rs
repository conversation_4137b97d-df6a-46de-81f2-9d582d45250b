use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use chrono::{DateTime, Utc};

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Transaction {
    pub id: i64,
    #[serde(rename = "transactionNumber")]
    pub transaction_number: String,
    #[serde(rename = "transactionDate")]
    pub transaction_date: DateTime<Utc>,
    #[serde(rename = "transactionType")]
    pub transaction_type: TransactionType,
    #[serde(rename = "customerId")]
    pub customer_id: Option<i64>,
    #[serde(rename = "customerName")]
    pub customer_name: Option<String>,
    pub subtotal: f64,
    #[serde(rename = "discountAmount")]
    pub discount_amount: f64,
    #[serde(rename = "discountPercentage")]
    pub discount_percentage: f64,
    #[serde(rename = "taxAmount")]
    pub tax_amount: f64,
    #[serde(rename = "taxPercentage")]
    pub tax_percentage: f64,
    #[serde(rename = "totalAmount")]
    pub total_amount: f64,
    #[serde(rename = "paidAmount")]
    pub paid_amount: f64,
    #[serde(rename = "changeAmount")]
    pub change_amount: f64,
    pub status: TransactionStatus,
    #[serde(rename = "paymentStatus")]
    pub payment_status: PaymentStatus,
    #[serde(rename = "cashierId")]
    pub cashier_id: i64,
    pub notes: Option<String>,
    #[serde(rename = "createdAt")]
    pub created_at: DateTime<Utc>,
    #[serde(rename = "updatedAt")]
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct TransactionItem {
    pub id: i64,
    #[serde(rename = "transactionId")]
    pub transaction_id: i64,
    #[serde(rename = "productId")]
    pub product_id: i64,
    #[serde(rename = "productName")]
    pub product_name: String,
    #[serde(rename = "productSku")]
    pub product_sku: String,
    pub quantity: f64,
    #[serde(rename = "unitPrice")]
    pub unit_price: f64,
    #[serde(rename = "totalPrice")]
    pub total_price: f64,
    #[serde(rename = "discountAmount")]
    pub discount_amount: f64,
    #[serde(rename = "createdAt")]
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct PaymentMethod {
    pub id: i64,
    pub name: String,
    #[serde(rename = "paymentType")]
    pub payment_type: PaymentType,
    #[serde(rename = "isActive")]
    pub is_active: bool,
    #[serde(rename = "createdAt")]
    pub created_at: DateTime<Utc>,
    #[serde(rename = "updatedAt")]
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct TransactionPayment {
    pub id: i64,
    #[serde(rename = "transactionId")]
    pub transaction_id: i64,
    #[serde(rename = "paymentMethodId")]
    pub payment_method_id: i64,
    pub amount: f64,
    #[serde(rename = "referenceNumber")]
    pub reference_number: Option<String>,
    #[serde(rename = "createdAt")]
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "TEXT")]
pub enum TransactionType {
    #[sqlx(rename = "sale")]
    Sale,
    #[sqlx(rename = "return")]
    Return,
    #[sqlx(rename = "void")]
    Void,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "TEXT")]
pub enum TransactionStatus {
    #[sqlx(rename = "pending")]
    Pending,
    #[sqlx(rename = "completed")]
    Completed,
    #[sqlx(rename = "cancelled")]
    Cancelled,
    #[sqlx(rename = "refunded")]
    Refunded,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "TEXT")]
pub enum PaymentStatus {
    #[sqlx(rename = "unpaid")]
    Unpaid,
    #[sqlx(rename = "partial")]
    Partial,
    #[sqlx(rename = "paid")]
    Paid,
    #[sqlx(rename = "overpaid")]
    Overpaid,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "TEXT")]
pub enum PaymentType {
    #[sqlx(rename = "cash")]
    Cash,
    #[sqlx(rename = "card")]
    Card,
    #[sqlx(rename = "digital")]
    Digital,
}

#[derive(Debug, Deserialize)]
pub struct CreateTransactionRequest {
    #[serde(rename = "customerId")]
    pub customer_id: Option<i64>,
    #[serde(rename = "customerName")]
    pub customer_name: Option<String>,
    pub items: Vec<CreateTransactionItemRequest>,
    #[serde(rename = "discountAmount")]
    pub discount_amount: Option<f64>,
    #[serde(rename = "discountPercentage")]
    pub discount_percentage: Option<f64>,
    #[serde(rename = "taxPercentage")]
    pub tax_percentage: Option<f64>,
    pub payments: Vec<CreateTransactionPaymentRequest>,
    pub notes: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct CreateTransactionItemRequest {
    #[serde(rename = "productId")]
    pub product_id: i64,
    pub quantity: f64,
    #[serde(rename = "unitPrice")]
    pub unit_price: f64,
    #[serde(rename = "discountAmount")]
    pub discount_amount: Option<f64>,
}

#[derive(Debug, Deserialize)]
pub struct CreateTransactionPaymentRequest {
    #[serde(rename = "paymentMethodId")]
    pub payment_method_id: i64,
    pub amount: f64,
    #[serde(rename = "referenceNumber")]
    pub reference_number: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct TransactionWithDetails {
    // #[serde(flatten)]
    pub transaction: Transaction,
    pub items: Vec<TransactionItem>,
    pub payments: Vec<TransactionPaymentWithMethod>,
}

#[derive(Debug, Serialize)]
pub struct TransactionPaymentWithMethod {
    // #[serde(flatten)]
    pub payment: TransactionPayment,
    #[serde(rename = "paymentMethod")]
    pub payment_method: PaymentMethod,
}

impl std::fmt::Display for TransactionType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            TransactionType::Sale => write!(f, "sale"),
            TransactionType::Return => write!(f, "return"),
            TransactionType::Void => write!(f, "void"),
        }
    }
}

impl std::fmt::Display for TransactionStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            TransactionStatus::Pending => write!(f, "pending"),
            TransactionStatus::Completed => write!(f, "completed"),
            TransactionStatus::Cancelled => write!(f, "cancelled"),
            TransactionStatus::Refunded => write!(f, "refunded"),
        }
    }
}

impl std::fmt::Display for PaymentStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            PaymentStatus::Unpaid => write!(f, "unpaid"),
            PaymentStatus::Partial => write!(f, "partial"),
            PaymentStatus::Paid => write!(f, "paid"),
            PaymentStatus::Overpaid => write!(f, "overpaid"),
        }
    }
}

impl std::fmt::Display for PaymentType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            PaymentType::Cash => write!(f, "cash"),
            PaymentType::Card => write!(f, "card"),
            PaymentType::Digital => write!(f, "digital"),
        }
    }
}
