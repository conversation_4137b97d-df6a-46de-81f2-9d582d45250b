use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use chrono::{DateTime, Utc, NaiveDate};

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Customer {
    pub id: i64,
    pub name: String,
    pub email: Option<String>,
    pub phone: Option<String>,
    pub address: Option<String>,
    #[serde(rename = "dateOfBirth")]
    pub date_of_birth: Option<NaiveDate>,
    #[serde(rename = "isActive")]
    pub is_active: bool,
    #[serde(rename = "createdAt")]
    pub created_at: DateTime<Utc>,
    #[serde(rename = "updatedAt")]
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Deserialize)]
pub struct CreateCustomerRequest {
    pub name: String,
    pub email: Option<String>,
    pub phone: Option<String>,
    pub address: Option<String>,
    #[serde(rename = "dateOfBirth")]
    pub date_of_birth: Option<NaiveDate>,
}

#[derive(Debug, Deserialize)]
pub struct UpdateCustomerRequest {
    pub name: Option<String>,
    pub email: Option<String>,
    pub phone: Option<String>,
    pub address: Option<String>,
    #[serde(rename = "dateOfBirth")]
    pub date_of_birth: Option<NaiveDate>,
    #[serde(rename = "isActive")]
    pub is_active: Option<bool>,
}

#[derive(Debug, Serialize)]
pub struct CustomerWithStats {
    #[serde(flatten)]
    pub customer: Customer,
    #[serde(rename = "totalTransactions")]
    pub total_transactions: i64,
    #[serde(rename = "totalSpent")]
    pub total_spent: f64,
    #[serde(rename = "lastTransactionDate")]
    pub last_transaction_date: Option<DateTime<Utc>>,
}
