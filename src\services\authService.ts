import { invoke } from '@tauri-apps/api/core';
import { User, ApiResponse } from '@/types';
import { getCurrentEnvironment } from '@/utils/environment';
import { mockApiService } from './mockApiService';

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface LoginResponse {
  user: User;
  token: string;
  refreshToken: string;
}

export class AuthService {
  private static instance: AuthService;
  private token: string | null = null;

  private constructor() { }

  public static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  public setToken(token: string | null): void {
    this.token = token;

    // Also set the token in mockApiService for browser environment
    const environment = getCurrentEnvironment();
    if (environment === 'browser') {
      mockApiService.setToken(token);
    }
  }

  public getToken(): string | null {
    return this.token;
  }

  public restoreTokenFromStorage(): void {
    try {
      const storedToken = localStorage.getItem('auth_token');
      if (storedToken) {
        this.token = storedToken;
      }

      // Also set the token in mockApiService for browser environment
      const environment = getCurrentEnvironment();
      if (environment === 'browser' && this.token) {
        mockApiService.setToken(this.token);
      }
    } catch (error) {
      console.warn('Failed to restore token from localStorage:', error);
    }
  }

  private saveTokenToStorage(token: string): void {
    try {
      localStorage.setItem('auth_token', token);
    } catch (error) {
      console.warn('Failed to save token to localStorage:', error);
    }
  }

  private removeTokenFromStorage(): void {
    try {
      localStorage.removeItem('auth_token');
    } catch (error) {
      console.warn('Failed to remove token from localStorage:', error);
    }
  }

  public async login(credentials: LoginCredentials): Promise<LoginResponse> {
    try {
      const environment = getCurrentEnvironment();

      if (environment === 'browser') {
        // Use mock API for browser environment
        console.log('Using mock API for browser environment');
        const response = await mockApiService.login(credentials);

        if (!response.success || !response.data) {
          throw new Error(response.error || 'Login failed');
        }

        this.setToken(response.data.token);
        this.saveTokenToStorage(response.data.token);
        return response.data;
      } else {
        // Use Tauri backend for native app
        const response = await invoke<ApiResponse<LoginResponse>>('login', {
          credentials: {
            username: credentials.username,
            password: credentials.password,
          },
        });

        if (!response.success || !response.data) {
          throw new Error(response.error || 'Login failed');
        }

        this.setToken(response.data.token);
        this.saveTokenToStorage(response.data.token);
        return response.data;
      }
    } catch (error) {
      console.error('Login error:', error);
      throw new Error('Login failed. Please check your credentials.');
    }
  }

  public async logout(): Promise<void> {
    try {
      const environment = getCurrentEnvironment();

      if (environment === 'browser') {
        // Use mock API for browser environment
        await mockApiService.logout();
      } else {
        // Use Tauri backend for native app
        const refreshToken = localStorage.getItem('refresh_token') || '';
        await invoke<ApiResponse<void>>('logout', { refresh_token: refreshToken });
      }
    } catch (error) {
      console.warn('Logout error:', error);
    } finally {
      this.setToken(null);
      this.removeTokenFromStorage();
      localStorage.removeItem('refresh_token');
    }
  }

  public async refreshToken(): Promise<string> {
    try {
      const stored = localStorage.getItem('refresh_token') || '';
      const response = await invoke<ApiResponse<{ token: string }>>('refresh_token', { refresh_token: stored });

      if (!response.success || !response.data) {
        throw new Error(response.error || 'Token refresh failed');
      }

      this.setToken(response.data.token);
      return response.data.token;
    } catch (error) {
      console.warn('Token refresh failed:', error);
      throw error;
    }
  }

  public async validateToken(): Promise<boolean> {
    if (!this.token) return false;

    try {
      const environment = getCurrentEnvironment();

      if (environment === 'browser') {
        // Use mock API for browser environment
        const response = await mockApiService.validateToken(this.token);
        return response.success && response.data === true;
      } else {
        // Use Tauri backend for native app
        const response = await invoke<ApiResponse<{ valid: boolean }>>('validate_token', {
          token: this.token,
        });
        return response.success && response.data?.valid === true;
      }
    } catch (error) {
      console.warn('Token validation failed:', error);
      return false;
    }
  }

  public async getCurrentUser(): Promise<User | null> {
    if (!this.token) return null;

    try {
      const environment = getCurrentEnvironment();

      if (environment === 'browser') {
        // Use mock API for browser environment
        const response = await mockApiService.getCurrentUser();
        return response.success ? (response.data || null) : null;
      } else {
        // Use Tauri backend for native app
        const response = await invoke<ApiResponse<User>>('get_current_user', {
          token: this.token,
        });

        if (!response.success || !response.data) {
          return null;
        }

        return response.data;
      }
    } catch (error) {
      console.warn('Failed to get current user:', error);
      return null;
    }
  }
}

export const authService = AuthService.getInstance();

