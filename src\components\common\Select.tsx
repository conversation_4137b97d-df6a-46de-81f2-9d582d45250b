import React, { forwardRef } from 'react';
import { ChevronDown } from 'lucide-react';

interface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  error?: string;
  label?: string;
  children: React.ReactNode;
}

const Select = forwardRef<HTMLSelectElement, SelectProps>(
  ({ error, label, className = '', children, ...props }, ref) => {
    const baseClasses = `
      w-full px-3 py-2 pr-10 border rounded-md 
      focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent
      appearance-none bg-white
      ${error 
        ? 'border-red-500 focus:ring-red-500' 
        : 'border-gray-300 hover:border-gray-400'
      }
      ${props.disabled ? 'bg-gray-100 cursor-not-allowed' : ''}
    `.trim().replace(/\s+/g, ' ');

    return (
      <div className="relative">
        {label && (
          <label className="block text-sm font-medium text-gray-700 mb-1">
            {label}
          </label>
        )}
        <div className="relative">
          <select
            ref={ref}
            className={`${baseClasses} ${className}`}
            {...props}
          >
            {children}
          </select>
          <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
            <ChevronDown className="h-4 w-4 text-gray-400" />
          </div>
        </div>
        {error && (
          <p className="mt-1 text-sm text-red-600">{error}</p>
        )}
      </div>
    );
  }
);

Select.displayName = 'Select';

export default Select;
