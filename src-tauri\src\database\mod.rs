use sqlx::{Pool, Sqlite, SqlitePool};

pub mod migrations;

pub type DbPool = Pool<Sqlite>;

pub async fn create_database_connection() -> Result<DbPool, sqlx::Error> {
    // Use a simple database path in the current directory
    let database_url = "sqlite:pos.db?mode=rwc";
    println!("Connecting to database at: {}", database_url);

    let pool = SqlitePool::connect(database_url).await?;

    // Run migrations with better error handling
    match migrations::run_migrations(&pool).await {
        Ok(_) => {
            println!("Database migrations completed successfully");
            Ok(pool)
        }
        Err(e) => {
            eprintln!("Migration failed: {}", e);

            // If migration fails due to foreign key constraints, try to reset the database
            if e.to_string().contains("FOREIGN KEY constraint failed") {
                eprintln!("Foreign key constraint error detected. Attempting database reset...");

                // Close the current pool
                pool.close().await;

                // Try to backup and reset the database
                match reset_database_with_backup().await {
                    Ok(new_pool) => {
                        println!("Database reset successful");
                        Ok(new_pool)
                    }
                    Err(reset_err) => {
                        eprintln!("Database reset failed: {}", reset_err);
                        Err(sqlx::Error::Configuration(format!("Migration error: {}. Reset failed: {}", e, reset_err).into()))
                    }
                }
            } else {
                Err(sqlx::Error::Configuration(format!("Migration error: {}", e).into()))
            }
        }
    }
}

async fn reset_database_with_backup() -> Result<DbPool, sqlx::Error> {
    use std::fs;
    use std::path::Path;

    let db_path = "pos.db";
    let backup_path = format!("pos_backup_{}.db", chrono::Utc::now().format("%Y%m%d_%H%M%S"));

    // Create backup if database exists
    if Path::new(db_path).exists() {
        if let Err(e) = fs::copy(db_path, &backup_path) {
            eprintln!("Warning: Could not create backup: {}", e);
        } else {
            println!("Database backed up to: {}", backup_path);
        }

        // Remove the old database
        if let Err(e) = fs::remove_file(db_path) {
            eprintln!("Warning: Could not remove old database: {}", e);
        }
    }

    // Create new connection and run migrations
    let database_url = "sqlite:pos.db?mode=rwc";
    let pool = SqlitePool::connect(database_url).await?;

    migrations::run_migrations(&pool).await.map_err(|e| {
        sqlx::Error::Configuration(format!("Migration error after reset: {}", e).into())
    })?;

    Ok(pool)
}