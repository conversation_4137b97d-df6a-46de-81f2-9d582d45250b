use sqlx::{Pool, Sqlite, SqlitePool};

pub mod migrations;

pub type DbPool = Pool<Sqlite>;

pub async fn create_database_connection() -> Result<DbPool, sqlx::Error> {
    // Use a simple database path in the current directory
    let database_url = "sqlite:pos.db?mode=rwc";
    println!("Connecting to database at: {}", database_url);

    let pool = SqlitePool::connect(database_url).await?;

    // Run migrations
    migrations::run_migrations(&pool).await.map_err(|e| {
        sqlx::Error::Configuration(format!("Migration error: {}", e).into())
    })?;

    Ok(pool)
}