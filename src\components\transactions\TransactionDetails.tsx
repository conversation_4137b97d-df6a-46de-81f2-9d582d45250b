import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { X, Receipt, User, Calendar, CreditCard, Package, Printer } from 'lucide-react';
import { format } from 'date-fns';

import { Transaction, transactionService } from '@/services/transactionService';
import { formatCurrency } from '@/lib/utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/common/Card';
import Button from '@/components/common/Button';
import Loading from '@/components/common/Loading';

interface TransactionDetailsProps {
  transaction: Transaction;
  onClose: () => void;
}

const TransactionDetails: React.FC<TransactionDetailsProps> = ({ transaction, onClose }) => {
  // Fetch detailed transaction data
  const { data: transactionDetails, isLoading } = useQuery({
    queryKey: ['transaction', transaction.id],
    queryFn: () => transactionService.getTransactionById(transaction.id),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const handlePrint = () => {
    // TODO: Implement print functionality
    console.log('Print transaction:', transaction.id);
  };

  if (isLoading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
        <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
          <div className="p-8">
            <Loading />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <Card className="border-0 shadow-none">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4 border-b">
            <CardTitle className="flex items-center gap-2">
              <Receipt className="h-5 w-5" />
              Transaction Details - #{transaction.transactionNumber}
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={handlePrint}>
                <Printer className="h-4 w-4 mr-1" />
                Print
              </Button>
              <Button variant="outline" size="sm" onClick={onClose}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          
          <CardContent className="p-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Transaction Information */}
              <div className="lg:col-span-2 space-y-6">
                {/* Basic Info */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Transaction Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center gap-3">
                      <Receipt className="h-5 w-5 text-gray-400" />
                      <div>
                        <p className="text-sm text-gray-500">Transaction Number</p>
                        <p className="font-medium">#{transaction.transactionNumber}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-3">
                      <Calendar className="h-5 w-5 text-gray-400" />
                      <div>
                        <p className="text-sm text-gray-500">Date & Time</p>
                        <p className="font-medium">
                          {format(new Date(transaction.transactionDate), 'MMM dd, yyyy HH:mm')}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-3">
                      <User className="h-5 w-5 text-gray-400" />
                      <div>
                        <p className="text-sm text-gray-500">Customer</p>
                        <p className="font-medium">{transaction.customerName || 'Walk-in Customer'}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-3">
                      <Package className="h-5 w-5 text-gray-400" />
                      <div>
                        <p className="text-sm text-gray-500">Type</p>
                        <p className="font-medium capitalize">{transaction.transactionType}</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Items */}
                {transactionDetails && 'items' in transactionDetails && (
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Items</h3>
                    <div className="border rounded-lg overflow-hidden">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                              Product
                            </th>
                            <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                              Qty
                            </th>
                            <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                              Price
                            </th>
                            <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                              Total
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {transactionDetails.items.map((item) => (
                            <tr key={item.id}>
                              <td className="px-4 py-3">
                                <div>
                                  <p className="text-sm font-medium text-gray-900">{item.productName}</p>
                                  <p className="text-sm text-gray-500">SKU: {item.productSku}</p>
                                </div>
                              </td>
                              <td className="px-4 py-3 text-right text-sm text-gray-900">
                                {item.quantity}
                              </td>
                              <td className="px-4 py-3 text-right text-sm text-gray-900">
                                {formatCurrency(item.unitPrice)}
                              </td>
                              <td className="px-4 py-3 text-right text-sm font-medium text-gray-900">
                                {formatCurrency(item.totalPrice)}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}

                {/* Payment Methods */}
                {transactionDetails && 'payments' in transactionDetails && transactionDetails.payments.length > 0 && (
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Payment Methods</h3>
                    <div className="space-y-2">
                      {transactionDetails.payments.map((payment) => (
                        <div key={payment.payment.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-center gap-3">
                            <CreditCard className="h-5 w-5 text-gray-400" />
                            <div>
                              <p className="font-medium">{payment.paymentMethod?.name || 'Unknown'}</p>
                              {payment.payment.referenceNumber && (
                                <p className="text-sm text-gray-500">Ref: {payment.payment.referenceNumber}</p>
                              )}
                            </div>
                          </div>
                          <p className="font-medium">{formatCurrency(payment.payment.amount)}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Notes */}
                {transaction.notes && (
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Notes</h3>
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <p className="text-sm text-gray-700">{transaction.notes}</p>
                    </div>
                  </div>
                )}
              </div>

              {/* Summary */}
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Summary</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Subtotal</span>
                      <span className="font-medium">{formatCurrency(transaction.subtotal)}</span>
                    </div>
                    
                    {transaction.discountAmount > 0 && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">
                          Discount {transaction.discountPercentage > 0 && `(${transaction.discountPercentage}%)`}
                        </span>
                        <span className="font-medium text-red-600">
                          -{formatCurrency(transaction.discountAmount)}
                        </span>
                      </div>
                    )}
                    
                    {transaction.taxAmount > 0 && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">
                          Tax {transaction.taxPercentage > 0 && `(${transaction.taxPercentage}%)`}
                        </span>
                        <span className="font-medium">{formatCurrency(transaction.taxAmount)}</span>
                      </div>
                    )}
                    
                    <div className="border-t pt-3">
                      <div className="flex justify-between">
                        <span className="text-lg font-medium">Total</span>
                        <span className="text-lg font-bold">{formatCurrency(transaction.totalAmount)}</span>
                      </div>
                    </div>
                    
                    <div className="flex justify-between">
                      <span className="text-gray-600">Paid</span>
                      <span className="font-medium">{formatCurrency(transaction.paidAmount)}</span>
                    </div>
                    
                    {transaction.changeAmount > 0 && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">Change</span>
                        <span className="font-medium">{formatCurrency(transaction.changeAmount)}</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Status */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Status</h3>
                  <div className="space-y-3">
                    <div>
                      <p className="text-sm text-gray-500 mb-1">Transaction Status</p>
                      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                        transaction.status === 'completed' ? 'text-green-600 bg-green-50' :
                        transaction.status === 'pending' ? 'text-yellow-600 bg-yellow-50' :
                        transaction.status === 'cancelled' ? 'text-red-600 bg-red-50' :
                        'text-gray-600 bg-gray-50'
                      }`}>
                        {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                      </span>
                    </div>
                    
                    <div>
                      <p className="text-sm text-gray-500 mb-1">Payment Status</p>
                      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                        transaction.paymentStatus === 'paid' ? 'text-green-600 bg-green-50' :
                        transaction.paymentStatus === 'partial' ? 'text-yellow-600 bg-yellow-50' :
                        transaction.paymentStatus === 'unpaid' ? 'text-red-600 bg-red-50' :
                        'text-blue-600 bg-blue-50'
                      }`}>
                        {transaction.paymentStatus.charAt(0).toUpperCase() + transaction.paymentStatus.slice(1)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default TransactionDetails;
