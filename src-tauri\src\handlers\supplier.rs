use crate::database::DbPool;
use crate::models::supplier::{Supplier, CreateSupplierRequest, UpdateSupplierRequest, SupplierWithStats};
use crate::utils::{AuthService, ApiResponse, AppError, AppResult, require_manager_or_admin};
use chrono::Utc;
use tauri::State;

#[tauri::command]
pub async fn get_suppliers(
    pool: State<'_, DbPool>,
    token: String,
) -> Result<ApiResponse<Vec<Supplier>>, String> {
    let result = get_suppliers_internal(&pool, token).await;
    
    match result {
        Ok(suppliers) => Ok(ApiResponse::success(suppliers)),
        Err(e) => Ok(e.into()),
    }
}

async fn get_suppliers_internal(pool: &DbPool, token: String) -> AppResult<Vec<Supplier>> {
    let _user_session = AuthService::extract_user_from_token(&token)?;

    let suppliers: Vec<Supplier> = sqlx::query_as(
        "SELECT id, name, contact_person, email, phone, address, is_active, created_at, updated_at
         FROM suppliers WHERE is_active = 1 ORDER BY name"
    )
    .fetch_all(pool)
    .await?;

    Ok(suppliers)
}

#[tauri::command]
pub async fn get_supplier_by_id(
    pool: State<'_, DbPool>,
    token: String,
    supplier_id: i64,
) -> Result<ApiResponse<Supplier>, String> {
    let result = get_supplier_by_id_internal(&pool, token, supplier_id).await;
    
    match result {
        Ok(supplier) => Ok(ApiResponse::success(supplier)),
        Err(e) => Ok(e.into()),
    }
}

async fn get_supplier_by_id_internal(pool: &DbPool, token: String, supplier_id: i64) -> AppResult<Supplier> {
    let _user_session = AuthService::extract_user_from_token(&token)?;

    let supplier: Supplier = sqlx::query_as(
        "SELECT id, name, contact_person, email, phone, address, is_active, created_at, updated_at
         FROM suppliers WHERE id = ? AND is_active = 1"
    )
    .bind(supplier_id)
    .fetch_optional(pool)
    .await?
    .ok_or(AppError::NotFound("Supplier not found".to_string()))?;

    Ok(supplier)
}

#[tauri::command]
pub async fn get_suppliers_with_stats(
    pool: State<'_, DbPool>,
    token: String,
) -> Result<ApiResponse<Vec<SupplierWithStats>>, String> {
    let result = get_suppliers_with_stats_internal(&pool, token).await;
    
    match result {
        Ok(suppliers) => Ok(ApiResponse::success(suppliers)),
        Err(e) => Ok(e.into()),
    }
}

async fn get_suppliers_with_stats_internal(pool: &DbPool, token: String) -> AppResult<Vec<SupplierWithStats>> {
    let _user_session = AuthService::extract_user_from_token(&token)?;

    // Fetch suppliers
    let suppliers: Vec<Supplier> = sqlx::query_as(
        "SELECT id, name, contact_person, email, phone, address, is_active, created_at, updated_at
         FROM suppliers WHERE is_active = 1 ORDER BY name"
    )
    .fetch_all(pool)
    .await?;

    // Fetch product counts by supplier
    let counts_rows: Vec<(i64, i64)> = sqlx::query_as(
        "SELECT COALESCE(supplier_id, 0) as supplier_id, COUNT(*) as product_count
         FROM products WHERE is_active = 1 GROUP BY supplier_id"
    )
    .fetch_all(pool)
    .await?;

    let counts_map: std::collections::HashMap<i64, i64> = counts_rows.into_iter().collect();

    let result: Vec<SupplierWithStats> = suppliers
        .into_iter()
        .map(|supplier| {
            let product_count = *counts_map.get(&supplier.id).unwrap_or(&0);
            SupplierWithStats {
                supplier,
                product_count,
            }
        })
        .collect();

    Ok(result)
}

#[tauri::command]
pub async fn create_supplier(
    pool: State<'_, DbPool>,
    token: String,
    request: CreateSupplierRequest,
) -> Result<ApiResponse<Supplier>, String> {
    let result = create_supplier_internal(&pool, token, request).await;
    
    match result {
        Ok(supplier) => Ok(ApiResponse::success(supplier)),
        Err(e) => Ok(e.into()),
    }
}

async fn create_supplier_internal(pool: &DbPool, token: String, request: CreateSupplierRequest) -> AppResult<Supplier> {
    let user_session = AuthService::extract_user_from_token(&token)?;
    // Check if user has permission (admin or manager)
    require_manager_or_admin(&user_session)?;

    // Check if supplier name already exists
    let existing_supplier: Option<Supplier> = sqlx::query_as(
        "SELECT id, name, contact_person, email, phone, address, is_active, created_at, updated_at
         FROM suppliers WHERE name = ? AND is_active = 1"
    )
    .bind(&request.name)
    .fetch_optional(pool)
    .await?;

    if existing_supplier.is_some() {
        return Err(AppError::Conflict("Supplier with this name already exists".to_string()));
    }

    let now = Utc::now();
    
    let supplier_id = sqlx::query(
        "INSERT INTO suppliers (name, contact_person, email, phone, address, is_active, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, 1, ?, ?)"
    )
    .bind(&request.name)
    .bind(&request.contact_person)
    .bind(&request.email)
    .bind(&request.phone)
    .bind(&request.address)
    .bind(now)
    .bind(now)
    .execute(pool)
    .await?
    .last_insert_rowid();

    let supplier: Supplier = sqlx::query_as(
        "SELECT id, name, contact_person, email, phone, address, is_active, created_at, updated_at
         FROM suppliers WHERE id = ?"
    )
    .bind(supplier_id)
    .fetch_one(pool)
    .await?;

    Ok(supplier)
}

#[tauri::command]
pub async fn update_supplier(
    pool: State<'_, DbPool>,
    token: String,
    supplier_id: i64,
    request: UpdateSupplierRequest,
) -> Result<ApiResponse<Supplier>, String> {
    let result = update_supplier_internal(&pool, token, supplier_id, request).await;
    
    match result {
        Ok(supplier) => Ok(ApiResponse::success(supplier)),
        Err(e) => Ok(e.into()),
    }
}

async fn update_supplier_internal(pool: &DbPool, token: String, supplier_id: i64, request: UpdateSupplierRequest) -> AppResult<Supplier> {
    let user_session = AuthService::extract_user_from_token(&token)?;
    // Check if user has permission (admin or manager)
    require_manager_or_admin(&user_session)?;

    // Check if supplier exists
    let existing_supplier: Supplier = sqlx::query_as(
        "SELECT id, name, contact_person, email, phone, address, is_active, created_at, updated_at
         FROM suppliers WHERE id = ?"
    )
    .bind(supplier_id)
    .fetch_optional(pool)
    .await?
    .ok_or(AppError::NotFound("Supplier not found".to_string()))?;

    // Check if new name conflicts with existing supplier (if name is being updated)
    if let Some(ref new_name) = request.name {
        if new_name != &existing_supplier.name {
            let name_conflict: Option<Supplier> = sqlx::query_as(
                "SELECT id, name, contact_person, email, phone, address, is_active, created_at, updated_at
                 FROM suppliers WHERE name = ? AND id != ? AND is_active = 1"
            )
            .bind(new_name)
            .bind(supplier_id)
            .fetch_optional(pool)
            .await?;

            if name_conflict.is_some() {
                return Err(AppError::Conflict("Supplier with this name already exists".to_string()));
            }
        }
    }

    let now = Utc::now();
    
    sqlx::query(
        "UPDATE suppliers 
         SET name = COALESCE(?, name),
             contact_person = COALESCE(?, contact_person),
             email = COALESCE(?, email),
             phone = COALESCE(?, phone),
             address = COALESCE(?, address),
             is_active = COALESCE(?, is_active),
             updated_at = ?
         WHERE id = ?"
    )
    .bind(&request.name)
    .bind(&request.contact_person)
    .bind(&request.email)
    .bind(&request.phone)
    .bind(&request.address)
    .bind(request.is_active)
    .bind(now)
    .bind(supplier_id)
    .execute(pool)
    .await?;

    let updated_supplier: Supplier = sqlx::query_as(
        "SELECT id, name, contact_person, email, phone, address, is_active, created_at, updated_at
         FROM suppliers WHERE id = ?"
    )
    .bind(supplier_id)
    .fetch_one(pool)
    .await?;

    Ok(updated_supplier)
}
