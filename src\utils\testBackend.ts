import { authService } from '@/services/authService';
import { productService } from '@/services/productService';
import { transactionService } from '@/services/transactionService';

export async function testBackendConnection() {
  try {
    console.log('Testing backend connection...');

    // Test login
    console.log('Testing login...');
    const loginResponse = await authService.login({
      username: 'admin',
      password: 'admin123'
    });
    console.log('Login successful:', loginResponse.user.username);

    // Test product fetch
    console.log('Testing product fetch...');
    const products = await productService.getProducts();
    console.log('Products fetched:', products.length);

    // Test transaction fetch
    console.log('Testing transaction fetch...');
    const transactions = await transactionService.getTransactions({ limit: 10 });
    console.log('Transactions fetched:', transactions.length);

    return {
      success: true,
      message: 'Backend connection successful',
      data: {
        user: loginResponse.user,
        productCount: products.length,
        transactionCount: transactions.length
      }
    };
  } catch (error) {
    console.error('Backend test failed:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error',
      data: null
    };
  }
}

export async function testEndToEndWorkflow() {
  try {
    console.log('Starting end-to-end workflow test...');

    // Step 1: Login
    console.log('Step 1: Login...');
    const loginResponse = await authService.login({
      username: 'admin',
      password: 'admin123'
    });
    console.log('✓ Login successful');

    // Step 2: Fetch products (simulating inventory management)
    console.log('Step 2: Fetch products...');
    const products = await productService.getProducts();
    console.log(`✓ Fetched ${products.length} products`);

    if (products.length === 0) {
      throw new Error('No products available for testing');
    }

    // Step 3: Create a test product (simulating inventory addition)
    console.log('Step 3: Create test product...');
    const testProduct = await productService.createProduct({
      sku: `TEST-${Date.now()}`,
      name: 'Test Product for E2E',
      description: 'This is a test product created during E2E testing',
      costPrice: 5000,
      sellingPrice: 8000,
      currentStock: 100,
      minStock: 10,
      unit: 'pcs',
      isTrackable: true,
      allowNegativeStock: false,
      isTaxable: true,
    });
    console.log(`✓ Created test product: ${testProduct.name}`);

    // Step 4: Create a transaction (simulating POS sale)
    console.log('Step 4: Create transaction...');
    const transaction = await transactionService.createTransaction({
      customerName: 'Test Customer',
      items: [{
        productId: testProduct.id,
        quantity: 2,
        unitPrice: testProduct.sellingPrice,
        discountAmount: 0,
      }],
      payments: [{
        paymentMethodId: 1, // Assuming cash payment method ID is 1
        amount: testProduct.sellingPrice * 2,
      }],
      discountAmount: 0,
      discountPercentage: 0,
      taxPercentage: 0,
      notes: 'E2E test transaction',
    }, loginResponse.user.id);
    console.log(`✓ Created transaction: ${transaction.transaction.transactionNumber}`);

    // Step 5: Fetch transaction history (simulating transaction history view)
    console.log('Step 5: Fetch transaction history...');
    const transactions = await transactionService.getTransactions({ limit: 10 });
    console.log(`✓ Fetched ${transactions.length} transactions`);

    // Step 6: Get transaction details
    console.log('Step 6: Get transaction details...');
    const transactionDetails = await transactionService.getTransactionById(transaction.transaction.id);
    console.log(`✓ Fetched transaction details for ${transactionDetails.transaction.transactionNumber}`);

    // Step 7: Update product information (simulating inventory management)
    console.log('Step 7: Update product information...');
    const updatedProduct = await productService.updateProduct(testProduct.id, {
      description: 'Updated description - E2E test completed',
    });
    console.log(`✓ Updated product description`);

    console.log('🎉 End-to-end workflow test completed successfully!');

    return {
      success: true,
      message: 'End-to-end workflow test completed successfully',
      data: {
        user: loginResponse.user,
        testProduct,
        transaction: transaction.transaction,
        transactionDetails,
        updatedProduct,
      }
    };
  } catch (error) {
    console.error('❌ End-to-end workflow test failed:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error',
      data: null
    };
  }
}
