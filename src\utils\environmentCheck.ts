/**
 * Environment check utility to verify Tauri availability
 */

export function checkEnvironment() {
  const hasWindow = typeof window !== 'undefined';
  const tauriV2 = hasWindow ? (window as any).__TAURI_INTERNALS__ : undefined;
  const tauriV1 = hasWindow ? (window as any).__TAURI__ : undefined;
  const isTauriAvailable = !!(tauriV2 || tauriV1);
  const isInvokeAvailable = !!(
    (tauriV2 && typeof tauriV2.invoke === 'function') ||
    (tauriV1 && typeof tauriV1.core?.invoke === 'function')
  );
  
  console.log('🔍 Environment Check:');
  console.log('- Window available:', typeof window !== 'undefined');
  console.log('- Tauri available:', isTauriAvailable);
  console.log('- __TAURI_INTERNALS__ present:', !!tauriV2);
  console.log('- __TAURI__ present:', !!tauriV1);
  console.log('- invoke available:', isInvokeAvailable);
  console.log('- User agent:', typeof window !== 'undefined' ? window.navigator.userAgent : 'N/A');
  console.log('- Location:', typeof window !== 'undefined' ? window.location.href : 'N/A');
  
  if (!isTauriAvailable) {
    console.warn('⚠️ Tauri is not available. This means:');
    console.warn('  - You are running in a regular browser, not the Tauri app');
    console.warn('  - Backend calls will fail because invoke() is not available');
    console.warn('  - To fix this, run the app through Tauri: npm run tauri dev');
  } else {
    console.log('✅ Tauri environment detected - backend calls should work');
  }
  
  return {
    isTauriAvailable,
    isInvokeAvailable,
    environment: isTauriAvailable ? 'tauri' : 'browser'
  };
}

// Auto-run check when this module is imported
if (typeof window !== 'undefined') {
  setTimeout(() => {
    checkEnvironment();
  }, 1000);
}
