import { invoke } from '@tauri-apps/api/core';
import { Customer, ApiResponse } from '@/types';
import { authService } from './authService';
import { getCurrentEnvironment } from '@/utils/environment';
import { mockApiService } from './mockApiService';

export interface CreateCustomerRequest {
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  dateOfBirth?: Date;
}

export interface UpdateCustomerRequest {
  name?: string;
  email?: string;
  phone?: string;
  address?: string;
  dateOfBirth?: Date;
  isActive?: boolean;
}

export interface CustomerWithStats {
  id: number;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  dateOfBirth?: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  totalTransactions: number;
  totalSpent: number;
  lastTransactionDate?: Date;
}

class CustomerService {
  private static instance: CustomerService;

  private constructor() {}

  public static getInstance(): CustomerService {
    if (!CustomerService.instance) {
      CustomerService.instance = new CustomerService();
    }
    return CustomerService.instance;
  }

  /**
   * Get all customers
   */
  async getCustomers(): Promise<Customer[]> {
    try {
      const environment = getCurrentEnvironment();

      if (environment === 'browser') {
        // Use mock API for browser environment
        console.log('Using mock API for customers in browser environment');
        const response = await mockApiService.getCustomers();

        if (!response.success || !response.data) {
          throw new Error(response.error || 'Failed to fetch customers');
        }

        return response.data;
      } else {
        // Use Tauri backend for native app
        const token = authService.getToken();
        if (!token) {
          throw new Error('No authentication token available');
        }

        const response = await invoke<ApiResponse<any[]>>('get_customers', {
          token,
        });

        if (!response.success || !response.data) {
          throw new Error(response.error || 'Failed to fetch customers');
        }

        return response.data.map(CustomerService.fromBackendCustomer);
      }
    } catch (error) {
      console.error('Get customers error:', error);
      throw new Error('Failed to fetch customers');
    }
  }

  /**
   * Get customer by ID
   */
  async getCustomerById(customerId: number): Promise<Customer> {
    try {
      const environment = getCurrentEnvironment();

      if (environment === 'browser') {
        // Use mock API for browser environment
        const response = await mockApiService.getCustomerById(customerId);

        if (!response.success || !response.data) {
          throw new Error(response.error || 'Customer not found');
        }

        return response.data;
      } else {
        // Use Tauri backend for native app
        const token = authService.getToken();
        if (!token) {
          throw new Error('No authentication token available');
        }

        const response = await invoke<ApiResponse<any>>('get_customer_by_id', {
          token,
          customerId,
        });

        if (!response.success || !response.data) {
          throw new Error(response.error || 'Failed to fetch customer');
        }

        return CustomerService.fromBackendCustomer(response.data);
      }
    } catch (error) {
      console.error('Get customer error:', error);
      throw new Error('Failed to fetch customer');
    }
  }

  /**
   * Get customers with statistics
   */
  async getCustomersWithStats(): Promise<CustomerWithStats[]> {
    try {
      const environment = getCurrentEnvironment();

      if (environment === 'browser') {
        // Use mock API for browser environment
        const response = await mockApiService.getCustomersWithStats();

        if (!response.success || !response.data) {
          throw new Error(response.error || 'Failed to fetch customers with stats');
        }

        return response.data;
      } else {
        // Use Tauri backend for native app
        const token = authService.getToken();
        if (!token) {
          throw new Error('No authentication token available');
        }

        const response = await invoke<ApiResponse<any[]>>('get_customers_with_stats', {
          token,
        });

        if (!response.success || !response.data) {
          throw new Error(response.error || 'Failed to fetch customers with stats');
        }

        return response.data.map(CustomerService.fromBackendCustomerWithStats);
      }
    } catch (error) {
      console.error('Get customers with stats error:', error);
      throw new Error('Failed to fetch customers with stats');
    }
  }

  /**
   * Create a new customer
   */
  async createCustomer(request: CreateCustomerRequest): Promise<Customer> {
    try {
      const environment = getCurrentEnvironment();

      if (environment === 'browser') {
        // Use mock API for browser environment
        const response = await mockApiService.createCustomer(request);
        return response;
      } else {
        // Use Tauri backend for native app
        const token = authService.getToken();
        if (!token) {
          throw new Error('No authentication token available');
        }

        const response = await invoke<ApiResponse<any>>('create_customer', {
          token,
          request: CustomerService.toBackendCreateRequest(request),
        });

        if (!response.success || !response.data) {
          throw new Error(response.error || 'Failed to create customer');
        }

        return CustomerService.fromBackendCustomer(response.data);
      }
    } catch (error) {
      console.error('Create customer error:', error);
      throw new Error('Failed to create customer');
    }
  }

  /**
   * Update an existing customer
   */
  async updateCustomer(customerId: number, request: UpdateCustomerRequest): Promise<Customer> {
    try {
      const environment = getCurrentEnvironment();

      if (environment === 'browser') {
        // Use mock API for browser environment
        const response = await mockApiService.updateCustomer(customerId, request);
        return response;
      } else {
        // Use Tauri backend for native app
        const token = authService.getToken();
        if (!token) {
          throw new Error('No authentication token available');
        }

        const response = await invoke<ApiResponse<any>>('update_customer', {
          token,
          customerId,
          request: CustomerService.toBackendUpdateRequest(request),
        });

        if (!response.success || !response.data) {
          throw new Error(response.error || 'Failed to update customer');
        }

        return CustomerService.fromBackendCustomer(response.data);
      }
    } catch (error) {
      console.error('Update customer error:', error);
      throw new Error('Failed to update customer');
    }
  }

  /**
   * Search customers by phone number
   */
  async searchCustomersByPhone(phone: string): Promise<Customer[]> {
    try {
      const environment = getCurrentEnvironment();

      if (environment === 'browser') {
        // Use mock API for browser environment
        const response = await mockApiService.searchCustomersByPhone(phone);

        if (!response.success || !response.data) {
          throw new Error(response.error || 'Failed to search customers');
        }

        return response.data;
      } else {
        // Use Tauri backend for native app
        const token = authService.getToken();
        if (!token) {
          throw new Error('No authentication token available');
        }

        const response = await invoke<ApiResponse<any[]>>('search_customers_by_phone', {
          token,
          phone,
        });

        if (!response.success || !response.data) {
          throw new Error(response.error || 'Failed to search customers');
        }

        return response.data.map(CustomerService.fromBackendCustomer);
      }
    } catch (error) {
      console.error('Search customers by phone error:', error);
      throw new Error('Failed to search customers');
    }
  }

  /**
   * Search customers by name
   */
  async searchCustomersByName(name: string): Promise<Customer[]> {
    try {
      const environment = getCurrentEnvironment();

      if (environment === 'browser') {
        // Use mock API for browser environment
        const response = await mockApiService.searchCustomersByName(name);

        if (!response.success || !response.data) {
          throw new Error(response.error || 'Failed to search customers');
        }

        return response.data;
      } else {
        // Use Tauri backend for native app
        const token = authService.getToken();
        if (!token) {
          throw new Error('No authentication token available');
        }

        const response = await invoke<ApiResponse<any[]>>('search_customers_by_name', {
          token,
          name,
        });

        if (!response.success || !response.data) {
          throw new Error(response.error || 'Failed to search customers');
        }

        return response.data.map(CustomerService.fromBackendCustomer);
      }
    } catch (error) {
      console.error('Search customers by name error:', error);
      throw new Error('Failed to search customers');
    }
  }

  /**
   * Convert backend customer to frontend format
   */
  private static fromBackendCustomer(backendCustomer: any): Customer {
    return {
      id: backendCustomer.id,
      name: backendCustomer.name,
      email: backendCustomer.email,
      phone: backendCustomer.phone,
      address: backendCustomer.address,
      dateOfBirth: backendCustomer.date_of_birth ? new Date(backendCustomer.date_of_birth) : undefined,
      isActive: backendCustomer.is_active,
      createdAt: new Date(backendCustomer.created_at),
      updatedAt: new Date(backendCustomer.updated_at),
    };
  }

  /**
   * Convert backend customer with stats to frontend format
   */
  private static fromBackendCustomerWithStats(backendCustomer: any): CustomerWithStats {
    return {
      id: backendCustomer.customer.id,
      name: backendCustomer.customer.name,
      email: backendCustomer.customer.email,
      phone: backendCustomer.customer.phone,
      address: backendCustomer.customer.address,
      dateOfBirth: backendCustomer.customer.date_of_birth ? new Date(backendCustomer.customer.date_of_birth) : undefined,
      isActive: backendCustomer.customer.is_active,
      createdAt: new Date(backendCustomer.customer.created_at),
      updatedAt: new Date(backendCustomer.customer.updated_at),
      totalTransactions: backendCustomer.total_transactions,
      totalSpent: backendCustomer.total_spent,
      lastTransactionDate: backendCustomer.last_transaction_date ? new Date(backendCustomer.last_transaction_date) : undefined,
    };
  }

  /**
   * Convert frontend create request to backend format
   */
  private static toBackendCreateRequest(request: CreateCustomerRequest): any {
    return {
      name: request.name,
      email: request.email,
      phone: request.phone,
      address: request.address,
      date_of_birth: request.dateOfBirth ? request.dateOfBirth.toISOString().split('T')[0] : undefined,
    };
  }

  /**
   * Convert frontend update request to backend format
   */
  private static toBackendUpdateRequest(request: UpdateCustomerRequest): any {
    return {
      name: request.name,
      email: request.email,
      phone: request.phone,
      address: request.address,
      date_of_birth: request.dateOfBirth ? request.dateOfBirth.toISOString().split('T')[0] : undefined,
      is_active: request.isActive,
    };
  }
}

export const customerService = CustomerService.getInstance();
