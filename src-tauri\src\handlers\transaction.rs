use crate::database::DbPool;
use crate::models::transaction::{
    Transaction, TransactionItem, TransactionPayment, TransactionWithDetails,
    TransactionPaymentWithMethod, PaymentMethod, CreateTransactionRequest,
    TransactionType, TransactionStatus, PaymentStatus
};
use crate::models::product::StockMovementType;
use crate::utils::{auth::generate_transaction_number, ApiResponse, AppError, AppResult};
use sqlx::Row;
use tauri::State;

#[tauri::command]
pub async fn create_transaction(
    pool: State<'_, DbPool>,
    request: CreateTransactionRequest,
    cashier_id: i64,
) -> Result<ApiResponse<TransactionWithDetails>, String> {
    let result = create_transaction_internal(&pool, request, cashier_id).await;
    
    match result {
        Ok(transaction) => Ok(ApiResponse::success(transaction)),
        Err(e) => Ok(e.into()),
    }
}

#[tauri::command]
pub async fn get_transactions(
    pool: State<'_, DbPool>,
    limit: Option<i64>,
    offset: Option<i64>,
    start_date: Option<String>,
    end_date: Option<String>,
    customer_id: Option<i64>,
    status: Option<String>,
) -> Result<ApiResponse<Vec<Transaction>>, String> {
    let result = get_transactions_internal(&pool, limit, offset, start_date, end_date, customer_id, status).await;
    
    match result {
        Ok(transactions) => Ok(ApiResponse::success(transactions)),
        Err(e) => Ok(e.into()),
    }
}

#[tauri::command]
pub async fn get_transaction_by_id(
    pool: State<'_, DbPool>,
    transaction_id: i64,
) -> Result<ApiResponse<TransactionWithDetails>, String> {
    let result = get_transaction_by_id_internal(&pool, transaction_id).await;
    
    match result {
        Ok(transaction) => Ok(ApiResponse::success(transaction)),
        Err(e) => Ok(e.into()),
    }
}

#[tauri::command]
pub async fn void_transaction(
    pool: State<'_, DbPool>,
    transaction_id: i64,
    cashier_id: i64,
    reason: Option<String>,
) -> Result<ApiResponse<Transaction>, String> {
    let result = void_transaction_internal(&pool, transaction_id, cashier_id, reason).await;
    
    match result {
        Ok(transaction) => Ok(ApiResponse::success(transaction)),
        Err(e) => Ok(e.into()),
    }
}

async fn create_transaction_internal(
    pool: &DbPool,
    request: CreateTransactionRequest,
    cashier_id: i64,
) -> AppResult<TransactionWithDetails> {
    // Start database transaction
    let mut tx = pool.begin().await?;
    
    // Validate items and calculate totals
    if request.items.is_empty() {
        return Err(AppError::Validation("Transaction must have at least one item".to_string()));
    }
    
    // Validate stock availability and calculate subtotal
    let mut subtotal = 0.0;
    for item in &request.items {
        // Check product exists and has sufficient stock
        let product_row = sqlx::query(
            "SELECT id, name, sku, selling_price, current_stock FROM products WHERE id = ? AND is_active = 1"
        )
        .bind(item.product_id)
        .fetch_optional(&mut *tx)
        .await?;

        let product_row = product_row.ok_or_else(||
            AppError::Validation(format!("Product with ID {} not found", item.product_id))
        )?;

        let stock_quantity: i64 = product_row.get("current_stock");
        if stock_quantity < item.quantity as i64 {
            let product_name: String = product_row.get("name");
            return Err(AppError::Validation(
                format!("Insufficient stock for product '{}'. Available: {}, Requested: {}",
                    product_name, stock_quantity, item.quantity)
            ));
        }
        
        let item_total = item.quantity * item.unit_price - item.discount_amount.unwrap_or(0.0);
        subtotal += item_total;
    }
    
    // Calculate totals
    let discount_amount = request.discount_amount.unwrap_or(0.0);
    let discount_percentage = request.discount_percentage.unwrap_or(0.0);
    let discount_from_percentage = subtotal * (discount_percentage / 100.0);
    let total_discount = discount_amount + discount_from_percentage;
    
    let after_discount = subtotal - total_discount;
    let tax_percentage = request.tax_percentage.unwrap_or(0.0);
    let tax_amount = after_discount * (tax_percentage / 100.0);
    let total_amount = after_discount + tax_amount;
    
    // Validate payments
    let total_payments: f64 = request.payments.iter().map(|p| p.amount).sum();
    if total_payments < total_amount {
        return Err(AppError::Validation(
            format!("Insufficient payment. Total: {:.2}, Paid: {:.2}", total_amount, total_payments)
        ));
    }
    
    let change_amount = total_payments - total_amount;
    let payment_status = if total_payments > total_amount {
        PaymentStatus::Overpaid
    } else {
        PaymentStatus::Paid
    };
    
    // Generate transaction number
    let transaction_number = generate_transaction_number();
    
    // Insert transaction
    let transaction_id = sqlx::query(
        r#"
        INSERT INTO transactions (
            transaction_number, transaction_type, customer_id, customer_name,
            subtotal, discount_amount, discount_percentage, tax_amount, tax_percentage,
            total_amount, paid_amount, change_amount, status, payment_status,
            cashier_id, notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        "#
    )
    .bind(&transaction_number)
    .bind(TransactionType::Sale.to_string())
    .bind(request.customer_id)
    .bind(&request.customer_name)
    .bind(subtotal)
    .bind(total_discount)
    .bind(discount_percentage)
    .bind(tax_amount)
    .bind(tax_percentage)
    .bind(total_amount)
    .bind(total_payments)
    .bind(change_amount)
    .bind(TransactionStatus::Completed.to_string())
    .bind(payment_status.to_string())
    .bind(cashier_id)
    .bind(&request.notes)
    .execute(&mut *tx)
    .await?
    .last_insert_rowid();
    
    // Insert transaction items and update inventory
    for item in &request.items {
        // Get product details
        let product_row = sqlx::query(
            "SELECT name, sku FROM products WHERE id = ?"
        )
        .bind(item.product_id)
        .fetch_one(&mut *tx)
        .await?;
        
        let product_name: String = product_row.get("name");
        let product_sku: String = product_row.get("sku");
        let item_total = item.quantity * item.unit_price - item.discount_amount.unwrap_or(0.0);
        
        // Insert transaction item
        sqlx::query(
            r#"
            INSERT INTO transaction_items (
                transaction_id, product_id, product_name, product_sku,
                quantity, unit_price, total_price, discount_amount
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            "#
        )
        .bind(transaction_id)
        .bind(item.product_id)
        .bind(&product_name)
        .bind(&product_sku)
        .bind(item.quantity)
        .bind(item.unit_price)
        .bind(item_total)
        .bind(item.discount_amount.unwrap_or(0.0))
        .execute(&mut *tx)
        .await?;
        
        // Update product stock
        sqlx::query(
            "UPDATE products SET current_stock = current_stock - ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?"
        )
        .bind(item.quantity as i64)
        .bind(item.product_id)
        .execute(&mut *tx)
        .await?;
        
        // Create stock movement record
        sqlx::query(
            r#"
            INSERT INTO stock_movements (
                product_id, movement_type, quantity, reference_type, reference_id, notes, created_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
            "#
        )
        .bind(item.product_id)
        .bind(StockMovementType::Out.to_string())
        .bind(item.quantity)
        .bind("transaction")
        .bind(transaction_id)
        .bind(format!("Sale - Transaction #{}", transaction_number))
        .bind(cashier_id)
        .execute(&mut *tx)
        .await?;
    }
    
    // Insert payments
    for payment in &request.payments {
        sqlx::query(
            r#"
            INSERT INTO transaction_payments (
                transaction_id, payment_method_id, amount, reference_number
            ) VALUES (?, ?, ?, ?)
            "#
        )
        .bind(transaction_id)
        .bind(payment.payment_method_id)
        .bind(payment.amount)
        .bind(&payment.reference_number)
        .execute(&mut *tx)
        .await?;
    }
    
    // Commit transaction
    tx.commit().await?;
    
    // Fetch and return the complete transaction
    get_transaction_by_id_internal(pool, transaction_id).await
}

async fn get_transactions_internal(
    pool: &DbPool,
    limit: Option<i64>,
    offset: Option<i64>,
    _start_date: Option<String>,
    _end_date: Option<String>,
    _customer_id: Option<i64>,
    _status: Option<String>,
) -> AppResult<Vec<Transaction>> {
    // For now, implement a simplified version without dynamic filtering
    // In production, you'd want to use a proper query builder like sqlx-query-builder
    let limit = limit.unwrap_or(100);
    let offset = offset.unwrap_or(0);

    let transactions = sqlx::query_as::<_, Transaction>(
        r#"
        SELECT id, transaction_number, transaction_date, transaction_type,
               customer_id, customer_name, subtotal, discount_amount, discount_percentage,
               tax_amount, tax_percentage, total_amount, paid_amount, change_amount,
               status, payment_status, cashier_id, notes, created_at, updated_at
        FROM transactions
        ORDER BY transaction_date DESC
        LIMIT ? OFFSET ?
        "#
    )
    .bind(limit)
    .bind(offset)
    .fetch_all(pool)
    .await?;

    Ok(transactions)
}

async fn get_transaction_by_id_internal(
    pool: &DbPool,
    transaction_id: i64,
) -> AppResult<TransactionWithDetails> {
    // Get transaction
    let transaction = sqlx::query_as::<_, Transaction>(
        r#"
        SELECT id, transaction_number, transaction_date, transaction_type,
               customer_id, customer_name, subtotal, discount_amount, discount_percentage,
               tax_amount, tax_percentage, total_amount, paid_amount, change_amount,
               status, payment_status, cashier_id, notes, created_at, updated_at
        FROM transactions
        WHERE id = ?
        "#
    )
    .bind(transaction_id)
    .fetch_optional(pool)
    .await?;

    let transaction = transaction.ok_or_else(||
        AppError::NotFound(format!("Transaction with ID {} not found", transaction_id))
    )?;

    // Get transaction items
    let items = sqlx::query_as::<_, TransactionItem>(
        r#"
        SELECT id, transaction_id, product_id, product_name, product_sku,
               quantity, unit_price, total_price, discount_amount, created_at
        FROM transaction_items
        WHERE transaction_id = ?
        ORDER BY id
        "#
    )
    .bind(transaction_id)
    .fetch_all(pool)
    .await?;

    // Get transaction payments with payment method details
    let payments = sqlx::query(
        r#"
        SELECT tp.id, tp.transaction_id, tp.payment_method_id, tp.amount,
               tp.reference_number, tp.created_at,
               pm.id as pm_id, pm.name as pm_name, pm.type as pm_type,
               pm.is_active as pm_is_active, pm.created_at as pm_created_at,
               pm.updated_at as pm_updated_at
        FROM transaction_payments tp
        JOIN payment_methods pm ON tp.payment_method_id = pm.id
        WHERE tp.transaction_id = ?
        ORDER BY tp.id
        "#
    )
    .bind(transaction_id)
    .fetch_all(pool)
    .await?;

    let payments_with_methods: Vec<TransactionPaymentWithMethod> = payments
        .into_iter()
        .map(|row| {
            let payment = TransactionPayment {
                id: row.get("id"),
                transaction_id: row.get("transaction_id"),
                payment_method_id: row.get("payment_method_id"),
                amount: row.get("amount"),
                reference_number: row.get("reference_number"),
                created_at: row.get("created_at"),
            };

            let payment_method = PaymentMethod {
                id: row.get("pm_id"),
                name: row.get("pm_name"),
                payment_type: row.get("pm_type"),
                is_active: row.get("pm_is_active"),
                created_at: row.get("pm_created_at"),
                updated_at: row.get("pm_updated_at"),
            };

            TransactionPaymentWithMethod {
                payment,
                payment_method,
            }
        })
        .collect();

    Ok(TransactionWithDetails {
        transaction,
        items,
        payments: payments_with_methods,
    })
}

async fn void_transaction_internal(
    pool: &DbPool,
    transaction_id: i64,
    cashier_id: i64,
    reason: Option<String>,
) -> AppResult<Transaction> {
    // Start database transaction
    let mut tx = pool.begin().await?;

    // Get the transaction to void
    let transaction = sqlx::query_as::<_, Transaction>(
        r#"
        SELECT id, transaction_number, transaction_date, transaction_type,
               customer_id, customer_name, subtotal, discount_amount, discount_percentage,
               tax_amount, tax_percentage, total_amount, paid_amount, change_amount,
               status, payment_status, cashier_id, notes, created_at, updated_at
        FROM transactions
        WHERE id = ? AND status = 'completed'
        "#
    )
    .bind(transaction_id)
    .fetch_optional(&mut *tx)
    .await?;

    let transaction = transaction.ok_or_else(||
        AppError::NotFound("Transaction not found or already voided".to_string())
    )?;

    // Get transaction items to restore inventory
    let items = sqlx::query_as::<_, TransactionItem>(
        r#"
        SELECT id, transaction_id, product_id, product_name, product_sku,
               quantity, unit_price, total_price, discount_amount, created_at
        FROM transaction_items
        WHERE transaction_id = ?
        "#
    )
    .bind(transaction_id)
    .fetch_all(&mut *tx)
    .await?;

    // Restore inventory for each item
    for item in &items {
        // Update product stock (add back the quantity)
        sqlx::query(
            "UPDATE products SET current_stock = current_stock + ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?"
        )
        .bind(item.quantity)
        .bind(item.product_id)
        .execute(&mut *tx)
        .await?;

        // Create stock movement record for the restoration
        sqlx::query(
            r#"
            INSERT INTO stock_movements (
                product_id, movement_type, quantity, reference_type, reference_id, notes, created_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
            "#
        )
        .bind(item.product_id)
        .bind(StockMovementType::In.to_string())
        .bind(item.quantity)
        .bind("void_transaction")
        .bind(transaction_id)
        .bind(format!("Void - Transaction #{} - {}",
            transaction.transaction_number,
            reason.as_deref().unwrap_or("No reason provided")))
        .bind(cashier_id)
        .execute(&mut *tx)
        .await?;
    }

    // Update transaction status to cancelled
    let void_notes = format!("VOIDED by cashier {} - {}",
        cashier_id,
        reason.as_deref().unwrap_or("No reason provided"));

    sqlx::query(
        r#"
        UPDATE transactions
        SET status = 'cancelled',
            payment_status = 'unpaid',
            notes = CASE
                WHEN notes IS NULL THEN ?
                ELSE notes || ' | ' || ?
            END,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
        "#
    )
    .bind(&void_notes)
    .bind(&void_notes)
    .bind(transaction_id)
    .execute(&mut *tx)
    .await?;

    // Commit the transaction
    tx.commit().await?;

    // Fetch and return the updated transaction
    let voided_transaction = sqlx::query_as::<_, Transaction>(
        r#"
        SELECT id, transaction_number, transaction_date, transaction_type,
               customer_id, customer_name, subtotal, discount_amount, discount_percentage,
               tax_amount, tax_percentage, total_amount, paid_amount, change_amount,
               status, payment_status, cashier_id, notes, created_at, updated_at
        FROM transactions
        WHERE id = ?
        "#
    )
    .bind(transaction_id)
    .fetch_one(pool)
    .await?;

    Ok(voided_transaction)
}
