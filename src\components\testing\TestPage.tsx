import React, { useState } from 'react';
import { <PERSON>, CheckCircle, XCircle, Clock, AlertTriangle, FileText } from 'lucide-react';
import { toast } from 'sonner';

import { testBackendConnection, testEndToEndWorkflow } from '@/utils/testBackend';
import { quickSystemTest } from '@/utils/quickTest';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/common/Card';
import Button from '@/components/common/Button';
import ImplementationSummary from './ImplementationSummary';

interface TestResult {
  success: boolean;
  message: string;
  data?: any;
  duration?: number;
  error?: any;
}

const TestPage: React.FC = () => {
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [isTestingE2E, setIsTestingE2E] = useState(false);
  const [isTestingQuick, setIsTestingQuick] = useState(false);
  const [connectionResult, setConnectionResult] = useState<TestResult | null>(null);
  const [e2eResult, setE2eResult] = useState<TestResult | null>(null);
  const [quickResult, setQuickResult] = useState<TestResult | null>(null);
  const [showSummary, setShowSummary] = useState(false);

  const handleTestConnection = async () => {
    setIsTestingConnection(true);
    setConnectionResult(null);
    
    const startTime = Date.now();
    try {
      const result = await testBackendConnection();
      const duration = Date.now() - startTime;
      
      setConnectionResult({ ...result, duration });
      
      if (result.success) {
        toast.success('Backend connection test passed');
      } else {
        toast.error('Backend connection test failed');
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      setConnectionResult({
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error',
        data: null,
        duration,
      });
      toast.error('Backend connection test failed');
    } finally {
      setIsTestingConnection(false);
    }
  };

  const handleTestE2E = async () => {
    setIsTestingE2E(true);
    setE2eResult(null);

    const startTime = Date.now();
    try {
      const result = await testEndToEndWorkflow();
      const duration = Date.now() - startTime;

      setE2eResult({ ...result, duration });

      if (result.success) {
        toast.success('End-to-end test passed');
      } else {
        toast.error('End-to-end test failed');
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      setE2eResult({
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error',
        data: null,
        duration,
      });
      toast.error('End-to-end test failed');
    } finally {
      setIsTestingE2E(false);
    }
  };

  const handleQuickTest = async () => {
    setIsTestingQuick(true);
    setQuickResult(null);

    const startTime = Date.now();
    try {
      const result = await quickSystemTest();
      const duration = Date.now() - startTime;

      setQuickResult({ ...result, duration });

      if (result.success) {
        toast.success('Quick test passed');
      } else {
        toast.error('Quick test failed');
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      setQuickResult({
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error',
        data: null,
        duration,
      });
      toast.error('Quick test failed');
    } finally {
      setIsTestingQuick(false);
    }
  };

  const getStatusIcon = (result: TestResult | null, isRunning: boolean) => {
    if (isRunning) {
      return <Clock className="h-5 w-5 text-blue-600 animate-spin" />;
    }
    if (!result) {
      return <AlertTriangle className="h-5 w-5 text-gray-400" />;
    }
    return result.success ? 
      <CheckCircle className="h-5 w-5 text-green-600" /> : 
      <XCircle className="h-5 w-5 text-red-600" />;
  };

  const getStatusColor = (result: TestResult | null, isRunning: boolean) => {
    if (isRunning) return 'text-blue-600 bg-blue-50';
    if (!result) return 'text-gray-600 bg-gray-50';
    return result.success ? 'text-green-600 bg-green-50' : 'text-red-600 bg-red-50';
  };

  if (showSummary) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Implementation Summary</h1>
            <p className="text-gray-600">Complete overview of implemented features and improvements</p>
          </div>
          <Button
            variant="outline"
            onClick={() => setShowSummary(false)}
          >
            Back to Testing
          </Button>
        </div>
        <ImplementationSummary />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">System Testing</h1>
          <p className="text-gray-600">Test the application's core functionality and backend integration</p>
        </div>
        <Button
          variant="outline"
          onClick={() => setShowSummary(true)}
          className="flex items-center gap-2"
        >
          <FileText className="h-4 w-4" />
          View Summary
        </Button>
      </div>

      {/* Test Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Quick System Test */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {getStatusIcon(quickResult, isTestingQuick)}
              Quick System Test
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-gray-600">
              Fast test to verify that the system works without mock data. Tests auth, products, and transactions.
            </p>

            <Button
              onClick={handleQuickTest}
              disabled={isTestingQuick}
              className="w-full flex items-center gap-2"
            >
              <Play className="h-4 w-4" />
              {isTestingQuick ? 'Testing...' : 'Run Quick Test'}
            </Button>

            {quickResult && (
              <div className={`p-3 rounded-lg ${getStatusColor(quickResult, false)}`}>
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium">
                    {quickResult.success ? 'Test Passed' : 'Test Failed'}
                  </span>
                  {quickResult.duration && (
                    <span className="text-sm">
                      {quickResult.duration}ms
                    </span>
                  )}
                </div>
                <p className="text-sm">{quickResult.message}</p>
                {quickResult.data && (
                  <div className="mt-2 text-xs">
                    <p>User: {quickResult.data.user?.username}</p>
                    <p>Products: {quickResult.data.productCount}</p>
                    <p>Test Product: {quickResult.data.testProduct?.name}</p>
                    <p>Test Transaction: #{quickResult.data.testTransaction?.transaction_number}</p>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
        {/* Backend Connection Test */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {getStatusIcon(connectionResult, isTestingConnection)}
              Backend Connection Test
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-gray-600">
              Tests basic connectivity to the backend API including authentication, product fetching, and transaction history.
            </p>
            
            <Button 
              onClick={handleTestConnection}
              disabled={isTestingConnection}
              className="w-full flex items-center gap-2"
            >
              <Play className="h-4 w-4" />
              {isTestingConnection ? 'Testing...' : 'Run Connection Test'}
            </Button>

            {connectionResult && (
              <div className={`p-3 rounded-lg ${getStatusColor(connectionResult, false)}`}>
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium">
                    {connectionResult.success ? 'Test Passed' : 'Test Failed'}
                  </span>
                  {connectionResult.duration && (
                    <span className="text-sm">
                      {connectionResult.duration}ms
                    </span>
                  )}
                </div>
                <p className="text-sm">{connectionResult.message}</p>
                {connectionResult.data && (
                  <div className="mt-2 text-xs">
                    <p>User: {connectionResult.data.user?.username}</p>
                    <p>Products: {connectionResult.data.productCount}</p>
                    <p>Transactions: {connectionResult.data.transactionCount}</p>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* End-to-End Test */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {getStatusIcon(e2eResult, isTestingE2E)}
              End-to-End Workflow Test
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-gray-600">
              Tests the complete workflow: login → create product → create transaction → view transaction history → update product.
            </p>
            
            <Button 
              onClick={handleTestE2E}
              disabled={isTestingE2E}
              className="w-full flex items-center gap-2"
            >
              <Play className="h-4 w-4" />
              {isTestingE2E ? 'Testing...' : 'Run E2E Test'}
            </Button>

            {e2eResult && (
              <div className={`p-3 rounded-lg ${getStatusColor(e2eResult, false)}`}>
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium">
                    {e2eResult.success ? 'Test Passed' : 'Test Failed'}
                  </span>
                  {e2eResult.duration && (
                    <span className="text-sm">
                      {e2eResult.duration}ms
                    </span>
                  )}
                </div>
                <p className="text-sm">{e2eResult.message}</p>
                {e2eResult.data && (
                  <div className="mt-2 text-xs space-y-1">
                    <p>Test Product: {e2eResult.data.testProduct?.name}</p>
                    <p>Transaction: #{e2eResult.data.transaction?.transaction_number}</p>
                    <p>Updated Product: {e2eResult.data.updatedProduct?.name}</p>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Test Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Testing Instructions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-medium text-gray-900 mb-2">Backend Connection Test</h3>
            <ul className="text-sm text-gray-600 space-y-1 list-disc list-inside">
              <li>Tests authentication with admin credentials</li>
              <li>Verifies product API endpoints</li>
              <li>Checks transaction history API</li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-medium text-gray-900 mb-2">End-to-End Workflow Test</h3>
            <ul className="text-sm text-gray-600 space-y-1 list-disc list-inside">
              <li>Creates a test product in inventory</li>
              <li>Simulates a POS transaction</li>
              <li>Verifies transaction appears in history</li>
              <li>Updates product information</li>
              <li>Tests complete data flow between all components</li>
            </ul>
          </div>

          <div className="p-3 bg-yellow-50 rounded-lg">
            <p className="text-sm text-yellow-800">
              <strong>Note:</strong> These tests will create actual data in your database. 
              The test product and transaction will be real entries that you can see in the inventory and transaction history.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TestPage;
