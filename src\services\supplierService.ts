import { invoke } from '@tauri-apps/api/core';
import { Supplier, ApiResponse } from '@/types';
import { authService } from './authService';
import { getCurrentEnvironment } from '@/utils/environment';
import { mockApiService } from './mockApiService';

export interface CreateSupplierRequest {
  name: string;
  contactPerson?: string;
  email?: string;
  phone?: string;
  address?: string;
}

export interface UpdateSupplierRequest {
  name?: string;
  contactPerson?: string;
  email?: string;
  phone?: string;
  address?: string;
  isActive?: boolean;
}

export interface SupplierWithStats {
  id: number;
  name: string;
  contactPerson?: string;
  email?: string;
  phone?: string;
  address?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  productCount: number;
}

class SupplierService {
  private static instance: SupplierService;

  private constructor() {}

  public static getInstance(): SupplierService {
    if (!SupplierService.instance) {
      SupplierService.instance = new SupplierService();
    }
    return SupplierService.instance;
  }

  /**
   * Get all suppliers
   */
  async getSuppliers(): Promise<Supplier[]> {
    try {
      const environment = getCurrentEnvironment();

      if (environment === 'browser') {
        // Use mock API for browser environment
        console.log('Using mock API for suppliers in browser environment');
        const response = await mockApiService.getSuppliers();

        if (!response.success || !response.data) {
          throw new Error(response.error || 'Failed to fetch suppliers');
        }

        return response.data;
      } else {
        // Use Tauri backend for native app
        const token = authService.getToken();
        if (!token) {
          throw new Error('No authentication token available');
        }

        const response = await invoke<ApiResponse<any[]>>('get_suppliers', {
          token,
        });

        if (!response.success || !response.data) {
          throw new Error(response.error || 'Failed to fetch suppliers');
        }

        return response.data.map(SupplierService.fromBackendSupplier);
      }
    } catch (error) {
      console.error('Get suppliers error:', error);
      throw new Error('Failed to fetch suppliers');
    }
  }

  /**
   * Get supplier by ID
   */
  async getSupplierById(supplierId: number): Promise<Supplier> {
    try {
      const environment = getCurrentEnvironment();

      if (environment === 'browser') {
        // Use mock API for browser environment
        const response = await mockApiService.getSupplierById(supplierId);

        if (!response.success || !response.data) {
          throw new Error(response.error || 'Supplier not found');
        }

        return response.data;
      } else {
        // Use Tauri backend for native app
        const token = authService.getToken();
        if (!token) {
          throw new Error('No authentication token available');
        }

        const response = await invoke<ApiResponse<any>>('get_supplier_by_id', {
          token,
          supplierId,
        });

        if (!response.success || !response.data) {
          throw new Error(response.error || 'Failed to fetch supplier');
        }

        return SupplierService.fromBackendSupplier(response.data);
      }
    } catch (error) {
      console.error('Get supplier error:', error);
      throw new Error('Failed to fetch supplier');
    }
  }

  /**
   * Get suppliers with statistics
   */
  async getSuppliersWithStats(): Promise<SupplierWithStats[]> {
    try {
      const environment = getCurrentEnvironment();

      if (environment === 'browser') {
        // Use mock API for browser environment
        const response = await mockApiService.getSuppliersWithStats();

        if (!response.success || !response.data) {
          throw new Error(response.error || 'Failed to fetch suppliers with stats');
        }

        return response.data;
      } else {
        // Use Tauri backend for native app
        const token = authService.getToken();
        if (!token) {
          throw new Error('No authentication token available');
        }

        const response = await invoke<ApiResponse<any[]>>('get_suppliers_with_stats', {
          token,
        });

        if (!response.success || !response.data) {
          throw new Error(response.error || 'Failed to fetch suppliers with stats');
        }

        return response.data.map(SupplierService.fromBackendSupplierWithStats);
      }
    } catch (error) {
      console.error('Get suppliers with stats error:', error);
      throw new Error('Failed to fetch suppliers with stats');
    }
  }

  /**
   * Create a new supplier
   */
  async createSupplier(request: CreateSupplierRequest): Promise<Supplier> {
    try {
      const environment = getCurrentEnvironment();

      if (environment === 'browser') {
        // Use mock API for browser environment
        const response = await mockApiService.createSupplier(request);
        return response;
      } else {
        // Use Tauri backend for native app
        const token = authService.getToken();
        if (!token) {
          throw new Error('No authentication token available');
        }

        const response = await invoke<ApiResponse<any>>('create_supplier', {
          token,
          request: SupplierService.toBackendCreateRequest(request),
        });

        if (!response.success || !response.data) {
          throw new Error(response.error || 'Failed to create supplier');
        }

        return SupplierService.fromBackendSupplier(response.data);
      }
    } catch (error) {
      console.error('Create supplier error:', error);
      throw new Error('Failed to create supplier');
    }
  }

  /**
   * Update an existing supplier
   */
  async updateSupplier(supplierId: number, request: UpdateSupplierRequest): Promise<Supplier> {
    try {
      const environment = getCurrentEnvironment();

      if (environment === 'browser') {
        // Use mock API for browser environment
        const response = await mockApiService.updateSupplier(supplierId, request);
        return response;
      } else {
        // Use Tauri backend for native app
        const token = authService.getToken();
        if (!token) {
          throw new Error('No authentication token available');
        }

        const response = await invoke<ApiResponse<any>>('update_supplier', {
          token,
          supplierId,
          request: SupplierService.toBackendUpdateRequest(request),
        });

        if (!response.success || !response.data) {
          throw new Error(response.error || 'Failed to update supplier');
        }

        return SupplierService.fromBackendSupplier(response.data);
      }
    } catch (error) {
      console.error('Update supplier error:', error);
      throw new Error('Failed to update supplier');
    }
  }

  /**
   * Convert backend supplier to frontend format
   */
  private static fromBackendSupplier(backendSupplier: any): Supplier {
    return {
      id: backendSupplier.id,
      name: backendSupplier.name,
      contactPerson: backendSupplier.contact_person,
      email: backendSupplier.email,
      phone: backendSupplier.phone,
      address: backendSupplier.address,
      isActive: backendSupplier.is_active,
      createdAt: new Date(backendSupplier.created_at),
      updatedAt: new Date(backendSupplier.updated_at),
    };
  }

  /**
   * Convert backend supplier with stats to frontend format
   */
  private static fromBackendSupplierWithStats(backendSupplier: any): SupplierWithStats {
    return {
      id: backendSupplier.id,
      name: backendSupplier.name,
      contactPerson: backendSupplier.contact_person,
      email: backendSupplier.email,
      phone: backendSupplier.phone,
      address: backendSupplier.address,
      isActive: backendSupplier.is_active,
      createdAt: new Date(backendSupplier.created_at),
      updatedAt: new Date(backendSupplier.updated_at),
      productCount: backendSupplier.product_count,
    };
  }

  /**
   * Convert frontend create request to backend format
   */
  private static toBackendCreateRequest(request: CreateSupplierRequest): any {
    return {
      name: request.name,
      contact_person: request.contactPerson,
      email: request.email,
      phone: request.phone,
      address: request.address,
    };
  }

  /**
   * Convert frontend update request to backend format
   */
  private static toBackendUpdateRequest(request: UpdateSupplierRequest): any {
    return {
      name: request.name,
      contact_person: request.contactPerson,
      email: request.email,
      phone: request.phone,
      address: request.address,
      is_active: request.isActive,
    };
  }
}

export const supplierService = SupplierService.getInstance();
