use crate::database::DbPool;
use crate::models::user::{User, LoginRequest, LoginResponse};
use crate::utils::{AuthService, ApiResponse, AppError, AppResult};
use chrono::Utc;
use sqlx::Row;
use tauri::State;

#[tauri::command]
pub async fn login(
    pool: State<'_, DbPool>,
    credentials: LoginRequest,
) -> Result<ApiResponse<LoginResponse>, String> {
    let result = login_internal(&pool, credentials).await;
    
    match result {
        Ok(response) => Ok(ApiResponse::success(response)),
        Err(e) => Ok(e.into()),
    }
}

async fn login_internal(pool: &DbPool, credentials: LoginRequest) -> AppResult<LoginResponse> {
    // Find user by username
    let user_row = sqlx::query(
        "SELECT id, username, email, password_hash, full_name, role, is_active, last_login, created_at, updated_at
         FROM users WHERE username = ? AND is_active = 1"
    )
    .bind(&credentials.username)
    .fetch_optional(pool)
    .await?;

    let user_row = user_row.ok_or_else(|| AppError::Authentication("Invalid credentials".to_string()))?;

    // Verify password
    let password_hash: String = user_row.get("password_hash");
    let is_valid = AuthService::verify_password(&credentials.password, &password_hash)?;

    if !is_valid {
        return Err(AppError::Authentication("Invalid credentials".to_string()));
    }

    // Create user object
    let user = User {
        id: user_row.get("id"),
        username: user_row.get("username"),
        email: user_row.get("email"),
        password_hash: password_hash,
        full_name: user_row.get("full_name"),
        role: user_row.get("role"),
        is_active: user_row.get("is_active"),
        last_login: user_row.get("last_login"),
        created_at: user_row.get("created_at"),
        updated_at: user_row.get("updated_at"),
    };

    // Generate tokens
    let token = AuthService::generate_jwt_token(&user)?;
    let refresh_token = AuthService::generate_refresh_token(user.id);

    // Update last login
    sqlx::query("UPDATE users SET last_login = ? WHERE id = ?")
        .bind(Utc::now())
        .bind(user.id)
        .execute(pool)
        .await?;

    Ok(LoginResponse {
        user,
        token,
        refresh_token,
    })
}

#[tauri::command]
pub async fn logout(refresh_token: String) -> Result<ApiResponse<()>, String> {
    AuthService::revoke_refresh_token(&refresh_token);
    Ok(ApiResponse::success_with_message((), "Logged out successfully".to_string()))
}

#[tauri::command]
pub async fn refresh_token(
    pool: State<'_, DbPool>,
    refresh_token: String,
) -> Result<ApiResponse<LoginResponse>, String> {
    let result = refresh_token_internal(&pool, refresh_token).await;
    
    match result {
        Ok(response) => Ok(ApiResponse::success(response)),
        Err(e) => Ok(e.into()),
    }
}

async fn refresh_token_internal(pool: &DbPool, refresh_token: String) -> AppResult<LoginResponse> {
    // Verify refresh token
    let user_id = AuthService::verify_refresh_token(&refresh_token)?;

    // Get user from database
    let user: User = sqlx::query_as(
        "SELECT id, username, email, password_hash, full_name, role, is_active, last_login, created_at, updated_at 
         FROM users WHERE id = ? AND is_active = 1"
    )
    .bind(user_id)
    .fetch_optional(pool)
    .await?
    .ok_or_else(|| AppError::Authentication("User not found".to_string()))?;

    // Generate new tokens
    let new_token = AuthService::generate_jwt_token(&user)?;
    let new_refresh_token = AuthService::generate_refresh_token(user.id);

    // Revoke old refresh token
    AuthService::revoke_refresh_token(&refresh_token);

    Ok(LoginResponse {
        user,
        token: new_token,
        refresh_token: new_refresh_token,
    })
}

#[tauri::command]
pub async fn validate_token(token: String) -> Result<ApiResponse<bool>, String> {
    match AuthService::verify_jwt_token(&token) {
        Ok(_) => Ok(ApiResponse::success(true)),
        Err(_) => Ok(ApiResponse::success(false)),
    }
}

#[tauri::command]
pub async fn get_current_user(
    pool: State<'_, DbPool>,
    token: String,
) -> Result<ApiResponse<User>, String> {
    let result = get_current_user_internal(&pool, token).await;
    
    match result {
        Ok(user) => Ok(ApiResponse::success(user)),
        Err(e) => Ok(e.into()),
    }
}

async fn get_current_user_internal(pool: &DbPool, token: String) -> AppResult<User> {
    let user_session = AuthService::extract_user_from_token(&token)?;

    let user: User = sqlx::query_as(
        "SELECT id, username, email, password_hash, full_name, role, is_active, last_login, created_at, updated_at 
         FROM users WHERE id = ? AND is_active = 1"
    )
    .bind(user_session.user_id)
    .fetch_optional(pool)
    .await?
    .ok_or_else(|| AppError::Authentication("User not found".to_string()))?;

    Ok(user)
}
