import React from 'react';
import { Plus, Package } from 'lucide-react';
import { toast } from 'sonner';

import { Product } from '@/types';
import { usePOSStore } from '@/stores/posStore';
import { formatCurrency } from '@/lib/utils';
import { Card, CardContent } from '@/components/common/Card';
import Button from '@/components/common/Button';

interface ProductGridProps {
  products: Product[];
}

interface ProductCardProps {
  product: Product;
  onAddToCart: (product: Product) => void;
  cartItemQuantity?: number;
}

const ProductCard: React.FC<ProductCardProps> = ({ product, onAddToCart, cartItemQuantity = 0 }) => {
  const isLowStock = product.currentStock <= product.minStock;
  const isOutOfStock = product.currentStock === 0;
  const totalQuantityInCart = cartItemQuantity;
  const availableStock = product.currentStock - totalQuantityInCart;
  const canAddToCart = availableStock > 0 || product.allowNegativeStock;

  const handleAddToCart = () => {
    if (!canAddToCart) {
      toast.error(`Cannot add more. Only ${availableStock} ${product.unit} available in stock`);
      return;
    }

    onAddToCart(product);
    toast.success(`${product.name} added to cart`);
  };

  return (
    <Card className="hover:shadow-medium transition-shadow cursor-pointer group">
      <CardContent className="p-0">
        <div className="flex flex-col h-full">
          {/* Product Image Placeholder */}
          <div className="w-full h-20 bg-gray-100 rounded-md flex items-center justify-center mb-2">
            {product.imagePath ? (
              <img
                src={product.imagePath}
                alt={product.name}
                className="w-full h-full object-cover rounded-md"
              />
            ) : (
              <Package className="h-8 w-8 text-gray-400" />
            )}
          </div>

          {/* Product Info */}
          <div className="flex-1">
            <h3 className="font-semibold text-gray-900 text-xs mb-1 line-clamp-1">
              {product.name}
            </h3>

            {/* Stock Status */}
            <div className="flex items-center justify-between mb-2">
              <span className="text-xs text-gray-500">
                {product.currentStock} {product.unit}
              </span>
              {isLowStock && !isOutOfStock && (
                <span className="px-1.5 py-0.5 text-xs font-medium bg-yellow-100 text-yellow-800 rounded">
                  Low
                </span>
              )}
              {isOutOfStock && (
                <span className="px-1.5 py-0.5 text-xs font-medium bg-red-100 text-red-800 rounded">
                  Out
                </span>
              )}
            </div>

            {/* Price */}
            <div className="mb-2">
              <span className="text-sm font-bold text-gray-900">
                {formatCurrency(product.sellingPrice)}
              </span>
            </div>
          </div>

          {/* Add to Cart Button */}
          <Button
            onClick={handleAddToCart}
            disabled={!canAddToCart}
            size="sm"
            className="w-full group-hover:bg-primary-700 h-8 text-xs"
          >
            <Plus className="h-3 w-3 mr-1" />
            Add
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

const ProductGrid: React.FC<ProductGridProps> = ({ products }) => {
  const { addToCart, cart } = usePOSStore();

  if (products.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-gray-500">
        <Package className="h-16 w-16 mb-4" />
        <h3 className="text-lg font-medium mb-2">No products found</h3>
        <p className="text-sm">Try adjusting your search terms</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3">
      {products.map((product) => {
        const cartItem = cart.find(item => item.product.id === product.id);
        return (
          <ProductCard
            key={product.id}
            product={product}
            onAddToCart={addToCart}
            cartItemQuantity={cartItem?.quantity || 0}
          />
        );
      })}
    </div>
  );
};

export default ProductGrid;
