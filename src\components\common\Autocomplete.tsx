import React, { useState, useEffect, useRef, useCallback } from 'react';
import { ChevronDown, X, Plus } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface AutocompleteOption {
  id: number | string;
  label: string;
  value: any;
}

interface AutocompleteProps {
  label?: string;
  placeholder?: string;
  value?: AutocompleteOption | null;
  options?: AutocompleteOption[];
  onSearch?: (query: string) => Promise<AutocompleteOption[]>;
  onChange?: (option: AutocompleteOption | null) => void;
  onAddNew?: () => void;
  addNewLabel?: string;
  error?: string;
  disabled?: boolean;
  required?: boolean;
  className?: string;
  minSearchLength?: number;
  debounceMs?: number;
  noOptionsMessage?: string;
  loadingMessage?: string;
}

const Autocomplete: React.FC<AutocompleteProps> = ({
  label,
  placeholder = 'Search...',
  value,
  options = [],
  onSearch,
  onChange,
  onAddNew,
  addNewLabel = 'Add New',
  error,
  disabled = false,
  required = false,
  className,
  minSearchLength = 1,
  debounceMs = 300,
  noOptionsMessage = 'No options found',
  loadingMessage = 'Loading...',
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<AutocompleteOption[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [highlightedIndex, setHighlightedIndex] = useState(-1);

  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const debounceRef = useRef<NodeJS.Timeout | null>(null);

  // Use provided options or search results
  const displayOptions = onSearch ? searchResults : options;

  // Debounced search function
  const debouncedSearch = useCallback(
    async (query: string) => {
      if (!onSearch || query.length < minSearchLength) {
        setSearchResults([]);
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      try {
        const results = await onSearch(query);
        setSearchResults(results);
      } catch (error) {
        console.error('Search error:', error);
        setSearchResults([]);
      } finally {
        setIsLoading(false);
      }
    },
    [onSearch, minSearchLength]
  );

  // Handle search input changes
  useEffect(() => {
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    debounceRef.current = setTimeout(() => {
      debouncedSearch(searchQuery);
    }, debounceMs);

    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, [searchQuery, debouncedSearch, debounceMs]);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
        setHighlightedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) {
      if (e.key === 'ArrowDown' || e.key === 'Enter') {
        setIsOpen(true);
        e.preventDefault();
      }
      return;
    }

    const totalOptions = displayOptions.length + (onAddNew ? 1 : 0);

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setHighlightedIndex(prev => (prev + 1) % totalOptions);
        break;
      case 'ArrowUp':
        e.preventDefault();
        setHighlightedIndex(prev => (prev - 1 + totalOptions) % totalOptions);
        break;
      case 'Enter':
        e.preventDefault();
        if (highlightedIndex >= 0) {
          if (highlightedIndex < displayOptions.length) {
            handleOptionSelect(displayOptions[highlightedIndex]);
          } else if (onAddNew) {
            onAddNew();
          }
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setHighlightedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  const handleOptionSelect = (option: AutocompleteOption) => {
    onChange?.(option);
    setSearchQuery(option.label);
    setIsOpen(false);
    setHighlightedIndex(-1);
  };

  const handleClear = () => {
    onChange?.(null);
    setSearchQuery('');
    setIsOpen(false);
    setHighlightedIndex(-1);
    inputRef.current?.focus();
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
    
    if (!isOpen && query.length >= minSearchLength) {
      setIsOpen(true);
    }
    
    // Clear selection if user types something different
    if (value && query !== value.label) {
      onChange?.(null);
    }
  };

  const handleInputFocus = () => {
    setIsOpen(true);
    if (!onSearch && options.length > 0) {
      // For static options, show all when focused
      setSearchResults(options);
    }
  };

  // Display value in input
  const displayValue = value ? value.label : searchQuery;

  return (
    <div className={cn('relative', className)}>
      {label && (
        <label className="block text-sm font-medium text-gray-700 mb-1">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          value={displayValue}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          disabled={disabled}
          className={cn(
            'w-full px-3 py-2 pr-10 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
            error ? 'border-red-300' : 'border-gray-300',
            disabled && 'bg-gray-50 cursor-not-allowed'
          )}
        />
        
        <div className="absolute inset-y-0 right-0 flex items-center pr-2">
          {value && !disabled && (
            <button
              type="button"
              onClick={handleClear}
              className="p-1 hover:bg-gray-100 rounded"
            >
              <X className="h-4 w-4 text-gray-400" />
            </button>
          )}
          <ChevronDown
            className={cn(
              'h-4 w-4 text-gray-400 transition-transform',
              isOpen && 'transform rotate-180'
            )}
          />
        </div>
      </div>

      {error && (
        <p className="mt-1 text-sm text-red-600">{error}</p>
      )}

      {isOpen && (
        <div
          ref={dropdownRef}
          className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto"
        >
          {isLoading ? (
            <div className="px-3 py-2 text-sm text-gray-500">
              {loadingMessage}
            </div>
          ) : displayOptions.length > 0 ? (
            <>
              {displayOptions.map((option, index) => (
                <button
                  key={option.id}
                  type="button"
                  onClick={() => handleOptionSelect(option)}
                  className={cn(
                    'w-full px-3 py-2 text-left text-sm hover:bg-gray-100 focus:bg-gray-100 focus:outline-none',
                    highlightedIndex === index && 'bg-gray-100'
                  )}
                >
                  {option.label}
                </button>
              ))}
              {onAddNew && (
                <button
                  type="button"
                  onClick={onAddNew}
                  className={cn(
                    'w-full px-3 py-2 text-left text-sm text-blue-600 hover:bg-blue-50 focus:bg-blue-50 focus:outline-none border-t border-gray-200 flex items-center gap-2',
                    highlightedIndex === displayOptions.length && 'bg-blue-50'
                  )}
                >
                  <Plus className="h-4 w-4" />
                  {addNewLabel}
                </button>
              )}
            </>
          ) : (
            <div className="px-3 py-2 text-sm text-gray-500">
              {noOptionsMessage}
              {onAddNew && searchQuery && (
                <button
                  type="button"
                  onClick={onAddNew}
                  className="block w-full mt-2 px-2 py-1 text-left text-blue-600 hover:bg-blue-50 rounded flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  {addNewLabel}
                </button>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default Autocomplete;
