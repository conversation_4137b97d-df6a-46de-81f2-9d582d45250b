import React, { useState, useMemo, useCallback } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Search, Receipt, TrendingUp, DollarSign, ShoppingCart } from 'lucide-react';
import { format, subDays } from 'date-fns';

import { transactionService, Transaction } from '@/services/transactionService';
import { formatCurrency } from '@/lib/utils';
import { Card, CardContent } from '@/components/common/Card';
import Input from '@/components/common/Input';
import Button from '@/components/common/Button';
import Loading from '@/components/common/Loading';
import EmptyState from '@/components/common/EmptyState';
import TransactionList from './TransactionList';
import TransactionDetails from './TransactionDetails';

interface TransactionStats {
  totalTransactions: number;
  totalRevenue: number;
  averageOrderValue: number;
  todayTransactions: number;
}

const TransactionHistory: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'completed' | 'pending' | 'cancelled'>('all');
  const [dateRange, setDateRange] = useState<{ start: string; end: string }>({
    start: format(subDays(new Date(), 30), 'yyyy-MM-dd'),
    end: format(new Date(), 'yyyy-MM-dd'),
  });
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);
  const [showDetails, setShowDetails] = useState(false);

  // Fetch transactions
  const { data: transactions = [], isLoading, error } = useQuery({
    queryKey: ['transactions', dateRange, statusFilter],
    queryFn: () => transactionService.getTransactions({
      startDate: dateRange.start,
      endDate: dateRange.end,
      status: statusFilter === 'all' ? undefined : statusFilter,
      limit: 1000, // Get a large number for now
    }),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });

  // Calculate transaction stats
  const transactionStats: TransactionStats = useMemo(() => {
    const today = format(new Date(), 'yyyy-MM-dd');

    const stats = transactions.reduce(
      (acc, transaction) => {
        acc.totalTransactions += 1;

        if (transaction.status === 'completed') {
          acc.totalRevenue += transaction.totalAmount;
        }

        const transactionDate = format(new Date(transaction.transactionDate), 'yyyy-MM-dd');
        if (transactionDate === today) {
          acc.todayTransactions += 1;
        }

        return acc;
      },
      { totalTransactions: 0, totalRevenue: 0, averageOrderValue: 0, todayTransactions: 0 }
    );

    const completedTransactions = transactions.filter(t => t.status === 'completed');
    stats.averageOrderValue = completedTransactions.length > 0
      ? stats.totalRevenue / completedTransactions.length
      : 0;

    return stats;
  }, [transactions]);

  // Filter transactions based on search
  const filteredTransactions = useMemo(() => {
    if (!searchTerm) return transactions;

    const searchLower = searchTerm.toLowerCase();
    return transactions.filter(transaction =>
      transaction.transactionNumber.toLowerCase().includes(searchLower) ||
      (transaction.customerName && transaction.customerName.toLowerCase().includes(searchLower)) ||
      (transaction.notes && transaction.notes.toLowerCase().includes(searchLower))
    );
  }, [transactions, searchTerm]);

  const handleViewTransaction = useCallback((transaction: Transaction) => {
    setSelectedTransaction(transaction);
    setShowDetails(true);
  }, []);

  const handleCloseDetails = useCallback(() => {
    setShowDetails(false);
    setSelectedTransaction(null);
  }, []);

  if (isLoading) {
    return <Loading />;
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Receipt className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Transactions</h3>
          <p className="text-gray-500">Failed to load transaction history. Please try again.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Transaction History</h1>
          <p className="text-gray-600">View and manage all transactions</p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Receipt className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Transactions</p>
                <p className="text-2xl font-bold text-gray-900">{transactionStats.totalTransactions}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <DollarSign className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Revenue</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(transactionStats.totalRevenue)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TrendingUp className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Average Order</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(transactionStats.averageOrderValue)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ShoppingCart className="h-8 w-8 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Today's Sales</p>
                <p className="text-2xl font-bold text-gray-900">{transactionStats.todayTransactions}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  type="text"
                  placeholder="Search by transaction number, customer, or notes..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            {/* Date Range */}
            <div className="flex gap-2">
              <div>
                <label className="block text-xs text-gray-500 mb-1">From</label>
                <Input
                  type="date"
                  value={dateRange.start}
                  onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
                  className="w-40"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-1">To</label>
                <Input
                  type="date"
                  value={dateRange.end}
                  onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
                  className="w-40"
                />
              </div>
            </div>
            
            {/* Status Filter */}
            <div className="flex gap-2">
              <Button
                variant={statusFilter === 'all' ? 'primary' : 'outline'}
                onClick={() => setStatusFilter('all')}
                size="sm"
              >
                All
              </Button>
              <Button
                variant={statusFilter === 'completed' ? 'primary' : 'outline'}
                onClick={() => setStatusFilter('completed')}
                size="sm"
              >
                Completed
              </Button>
              <Button
                variant={statusFilter === 'pending' ? 'primary' : 'outline'}
                onClick={() => setStatusFilter('pending')}
                size="sm"
              >
                Pending
              </Button>
              <Button
                variant={statusFilter === 'cancelled' ? 'primary' : 'outline'}
                onClick={() => setStatusFilter('cancelled')}
                size="sm"
              >
                Cancelled
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Transaction List */}
      {filteredTransactions.length === 0 ? (
        <EmptyState
          icon={Receipt}
          title="No transactions found"
          description={
            searchTerm || statusFilter !== 'all'
              ? "No transactions match your current filters."
              : "No transactions found for the selected date range."
          }
        />
      ) : (
        <TransactionList
          transactions={filteredTransactions}
          onViewTransaction={handleViewTransaction}
        />
      )}

      {/* Transaction Details Modal */}
      {showDetails && selectedTransaction && (
        <TransactionDetails
          transaction={selectedTransaction}
          onClose={handleCloseDetails}
        />
      )}
    </div>
  );
};

export default TransactionHistory;
