{"name": "d-<PERSON><PERSON>", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-toast": "^1.2.14", "@tanstack/react-query": "^5.84.2", "@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "date-fns": "^4.1.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.539.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.62.0", "react-router-dom": "^7.8.0", "recharts": "^3.1.2", "sonner": "^2.0.7", "tailwindcss": "^3.4.17", "zod": "^4.0.17", "zustand": "^5.0.7"}, "devDependencies": {"@tauri-apps/cli": "^2", "@types/node": "^24.2.1", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@typescript-eslint/eslint-plugin": "^8.39.1", "@typescript-eslint/parser": "^8.39.1", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "postcss": "^8.5.6", "prettier": "^3.6.2", "typescript": "~5.8.3", "vite": "^7.0.4"}}