import { invoke } from '@tauri-apps/api/core';
import { Product, ApiResponse } from '@/types';
import { authService } from './authService';
import { getCurrentEnvironment } from '@/utils/environment';
import { mockApiService } from './mockApiService';

export interface CreateProductRequest {
  sku: string;
  barcode?: string;
  name: string;
  description?: string;
  categoryId?: number;
  supplierId?: number;
  costPrice: number;
  sellingPrice: number;
  wholesalePrice?: number;
  minSellingPrice?: number;
  currentStock: number;
  minStock: number;
  maxStock?: number;
  unit: string;
  weight?: number;
  dimensions?: string;
  imagePath?: string;
  isTrackable?: boolean;
  allowNegativeStock?: boolean;
  isTaxable?: boolean;
}

export interface UpdateProductRequest {
  sku?: string;
  barcode?: string;
  name?: string;
  description?: string;
  categoryId?: number;
  supplierId?: number;
  costPrice?: number;
  sellingPrice?: number;
  wholesalePrice?: number;
  minSellingPrice?: number;
  minStock?: number;
  maxStock?: number;
  unit?: string;
  weight?: number;
  dimensions?: string;
  imagePath?: string;
  isActive?: boolean;
  isTrackable?: boolean;
  allowNegativeStock?: boolean;
  isTaxable?: boolean;
}

export class ProductService {
  private static instance: ProductService;

  private constructor() { }

  public static getInstance(): ProductService {
    if (!ProductService.instance) {
      ProductService.instance = new ProductService();
    }
    return ProductService.instance;
  }

  private static fromBackendProduct(p: any): Product {
    return {
      id: p.id,
      sku: p.sku,
      barcode: p.barcode ?? undefined,
      name: p.name,
      description: p.description ?? undefined,
      categoryId: p.category_id ?? undefined,
      supplierId: p.supplier_id ?? undefined,
      costPrice: p.cost_price,
      sellingPrice: p.selling_price,
      wholesalePrice: p.wholesale_price ?? undefined,
      minSellingPrice: p.min_selling_price ?? undefined,
      currentStock: p.current_stock,
      minStock: p.min_stock,
      maxStock: p.max_stock ?? undefined,
      unit: p.unit,
      weight: p.weight ?? undefined,
      dimensions: p.dimensions ?? undefined,
      imagePath: p.image_path ?? undefined,
      isActive: p.is_active,
      isTrackable: p.is_trackable,
      allowNegativeStock: p.allow_negative_stock,
      isTaxable: p.is_taxable,
      createdAt: new Date(p.created_at),
      updatedAt: new Date(p.updated_at),
    };
  }

  private static toBackendCreateRequest(req: CreateProductRequest) {
    return {
      sku: req.sku,
      barcode: req.barcode ?? null,
      name: req.name,
      description: req.description ?? null,
      category_id: req.categoryId ?? null,
      supplier_id: req.supplierId ?? null,
      cost_price: req.costPrice,
      selling_price: req.sellingPrice,
      wholesale_price: req.wholesalePrice ?? null,
      min_selling_price: req.minSellingPrice ?? null,
      current_stock: req.currentStock,
      min_stock: req.minStock,
      max_stock: req.maxStock ?? null,
      unit: req.unit,
      weight: req.weight ?? null,
      dimensions: req.dimensions ?? null,
      image_path: req.imagePath ?? null,
      is_trackable: req.isTrackable ?? true,
      allow_negative_stock: req.allowNegativeStock ?? false,
      is_taxable: req.isTaxable ?? true,
    };
  }

  private static toBackendUpdateRequest(req: UpdateProductRequest) {
    const out: any = {};
    if (req.sku !== undefined) out.sku = req.sku;
    if (req.barcode !== undefined) out.barcode = req.barcode;
    if (req.name !== undefined) out.name = req.name;
    if (req.description !== undefined) out.description = req.description;
    if (req.categoryId !== undefined) out.category_id = req.categoryId;
    if (req.supplierId !== undefined) out.supplier_id = req.supplierId;
    if (req.costPrice !== undefined) out.cost_price = req.costPrice;
    if (req.sellingPrice !== undefined) out.selling_price = req.sellingPrice;
    if (req.wholesalePrice !== undefined) out.wholesale_price = req.wholesalePrice;
    if (req.minSellingPrice !== undefined) out.min_selling_price = req.minSellingPrice;
    if (req.minStock !== undefined) out.min_stock = req.minStock;
    if (req.maxStock !== undefined) out.max_stock = req.maxStock;
    if (req.unit !== undefined) out.unit = req.unit;
    if (req.weight !== undefined) out.weight = req.weight;
    if (req.dimensions !== undefined) out.dimensions = req.dimensions;
    if (req.imagePath !== undefined) out.image_path = req.imagePath;
    if (req.isActive !== undefined) out.is_active = req.isActive;
    if (req.isTrackable !== undefined) out.is_trackable = req.isTrackable;
    if (req.allowNegativeStock !== undefined) out.allow_negative_stock = req.allowNegativeStock;
    if (req.isTaxable !== undefined) out.is_taxable = req.isTaxable;
    return out;
  }

  public async getProducts(): Promise<Product[]> {
    try {
      const environment = getCurrentEnvironment();

      if (environment === 'browser') {
        // Use mock API for browser environment
        console.log('Using mock API for products in browser environment');
        const response = await mockApiService.getProducts();

        if (!response.success || !response.data) {
          throw new Error(response.error || 'Failed to fetch products');
        }

        return response.data;
      } else {
        // Use Tauri backend for native app
        const token = authService.getToken();
        if (!token) {
          throw new Error('No authentication token available');
        }

        const response = await invoke<ApiResponse<Product[]>>('get_products', {
          token,
        });

        if (!response.success || !response.data) {
          throw new Error(response.error || 'Failed to fetch products');
        }

        return response.data.map(ProductService.fromBackendProduct);
      }
    } catch (error) {
      console.error('Get products error:', error);
      throw new Error('Failed to fetch products');
    }
  }

  public async getProductById(productId: number): Promise<Product> {
    try {
      const token = authService.getToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      const response = await invoke<ApiResponse<any>>('get_product_by_id', {
        token,
        product_id: productId,
      });

      if (!response.success || !response.data) {
        throw new Error(response.error || 'Failed to fetch product');
      }

      return ProductService.fromBackendProduct(response.data);
    } catch (error) {
      console.error('Get product by ID error:', error);
      throw new Error('Failed to fetch product');
    }
  }

  public async getProductByBarcode(barcode: string): Promise<Product> {
    try {
      const environment = getCurrentEnvironment();

      if (environment === 'browser') {
        // Use mock API for browser environment
        const response = await mockApiService.getProductByBarcode(barcode);

        if (!response.success || !response.data) {
          throw new Error(response.error || 'Product not found');
        }

        return response.data;
      } else {
        // Use Tauri backend for native app
        const token = authService.getToken();
        if (!token) {
          throw new Error('No authentication token available');
        }

        const response = await invoke<ApiResponse<any>>('get_product_by_barcode', {
          token,
          barcode,
        });

        if (!response.success || !response.data) {
          throw new Error(response.error || 'Failed to fetch product');
        }

        return ProductService.fromBackendProduct(response.data);
      }
    } catch (error) {
      console.error('Get product by barcode error:', error);
      throw new Error('Failed to fetch product');
    }
  }

  public async createProduct(request: CreateProductRequest): Promise<Product> {
    try {
      const environment = getCurrentEnvironment();

      if (environment === 'browser') {
        // Use mock API for browser environment
        const response = await mockApiService.createProduct(request);
        return response;
      } else {
        // Use Tauri backend for native app
        const token = authService.getToken();
        if (!token) {
          throw new Error('No authentication token available');
        }

        console.log('Create product request:', request);
        const response = await invoke<ApiResponse<any>>('create_product', {
          token,
          request: ProductService.toBackendCreateRequest(request),
        });

        console.log('Create product response:', response);
        if (!response.success || !response.data) {
          throw new Error(response.error || 'Failed to create product');
        }

        return ProductService.fromBackendProduct(response.data);
      }
    } catch (error) {
      console.error('Create product error:', error);
      throw new Error('Failed to create product');
    }
  }

  public async updateProduct(productId: number, request: UpdateProductRequest): Promise<Product> {
    try {
      const environment = getCurrentEnvironment();

      if (environment === 'browser') {
        // Use mock API for browser environment
        const response = await mockApiService.updateProduct(productId, request);
        return response;
      } else {
        // Use Tauri backend for native app
        const token = authService.getToken();
        if (!token) {
          throw new Error('No authentication token available');
        }

        const response = await invoke<ApiResponse<any>>('update_product', {
          token,
          product_id: productId,
          request: ProductService.toBackendUpdateRequest(request),
        });

        if (!response.success || !response.data) {
          throw new Error(response.error || 'Failed to update product');
        }

        return ProductService.fromBackendProduct(response.data);
      }
    } catch (error) {
      console.error('Update product error:', error);
      throw new Error('Failed to update product');
    }
  }
}

export const productService = ProductService.getInstance();
