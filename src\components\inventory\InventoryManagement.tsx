import React, { useState, useMemo, useCallback } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { Search, Plus, Package, AlertTriangle, TrendingDown } from 'lucide-react';
import { toast } from 'sonner';

import { Product } from '@/types';
import { productService } from '@/services/productService';
import { formatCurrency } from '@/lib/utils';
import { Card, CardContent } from '@/components/common/Card';
import Input from '@/components/common/Input';
import Button from '@/components/common/Button';
import Loading from '@/components/common/Loading';
import EmptyState from '@/components/common/EmptyState';
import ProductForm from './ProductForm';
import ProductList from './ProductList';

interface InventoryStats {
  totalProducts: number;
  lowStockProducts: number;
  outOfStockProducts: number;
  totalValue: number;
}

const InventoryManagement: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [showProductForm, setShowProductForm] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [stockFilter, setStockFilter] = useState<'all' | 'low' | 'out'>('all');

  const queryClient = useQueryClient();

  // Fetch products
  const { data: products = [], isLoading, error } = useQuery({
    queryKey: ['products'],
    queryFn: productService.getProducts,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Calculate inventory stats
  const inventoryStats: InventoryStats = React.useMemo(() => {
    const stats = products.reduce(
      (acc, product) => {
        acc.totalProducts += 1;
        acc.totalValue += product.currentStock * product.costPrice;
        
        if (product.currentStock <= 0) {
          acc.outOfStockProducts += 1;
        } else if (product.currentStock <= product.minStock) {
          acc.lowStockProducts += 1;
        }
        
        return acc;
      },
      { totalProducts: 0, lowStockProducts: 0, outOfStockProducts: 0, totalValue: 0 }
    );
    
    return stats;
  }, [products]);

  // Filter products based on search and stock filter
  const filteredProducts = useMemo(() => {
    let filtered = products;

    // Apply search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchLower) ||
        product.sku.toLowerCase().includes(searchLower) ||
        (product.barcode && product.barcode.toLowerCase().includes(searchLower))
      );
    }

    // Apply stock filter
    if (stockFilter === 'low') {
      filtered = filtered.filter(product =>
        product.currentStock > 0 && product.currentStock <= product.minStock
      );
    } else if (stockFilter === 'out') {
      filtered = filtered.filter(product => product.currentStock <= 0);
    }

    return filtered;
  }, [products, searchTerm, stockFilter]);

  // Handle product creation/update
  const handleProductSave = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: ['products'] });
    setShowProductForm(false);
    setEditingProduct(null);
    toast.success(editingProduct ? 'Product updated successfully' : 'Product created successfully');
  }, [queryClient, editingProduct]);

  const handleEditProduct = useCallback((product: Product) => {
    setEditingProduct(product);
    setShowProductForm(true);
  }, []);

  const handleAddProduct = useCallback(() => {
    setEditingProduct(null);
    setShowProductForm(true);
  }, []);

  if (isLoading) {
    return <Loading />;
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Inventory</h3>
          <p className="text-gray-500">Failed to load products. Please try again.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Inventory Management</h1>
          <p className="text-gray-600">Manage your products and stock levels</p>
        </div>
        <Button onClick={handleAddProduct} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Add Product
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Package className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Products</p>
                <p className="text-2xl font-bold text-gray-900">{inventoryStats.totalProducts}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <AlertTriangle className="h-8 w-8 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Low Stock</p>
                <p className="text-2xl font-bold text-gray-900">{inventoryStats.lowStockProducts}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TrendingDown className="h-8 w-8 text-red-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Out of Stock</p>
                <p className="text-2xl font-bold text-gray-900">{inventoryStats.outOfStockProducts}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Package className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Value</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(inventoryStats.totalValue)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  type="text"
                  placeholder="Search products by name, SKU, or barcode..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Button
                variant={stockFilter === 'all' ? 'primary' : 'outline'}
                onClick={() => setStockFilter('all')}
                size="sm"
              >
                All
              </Button>
              <Button
                variant={stockFilter === 'low' ? 'primary' : 'outline'}
                onClick={() => setStockFilter('low')}
                size="sm"
                className="flex items-center gap-1"
              >
                <AlertTriangle className="h-3 w-3" />
                Low Stock
              </Button>
              <Button
                variant={stockFilter === 'out' ? 'primary' : 'outline'}
                onClick={() => setStockFilter('out')}
                size="sm"
                className="flex items-center gap-1"
              >
                <TrendingDown className="h-3 w-3" />
                Out of Stock
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Product List */}
      {filteredProducts.length === 0 ? (
        <EmptyState
          icon={Package}
          title="No products found"
          description={
            searchTerm || stockFilter !== 'all'
              ? "No products match your current filters."
              : "Get started by adding your first product."
          }
          action={
            searchTerm || stockFilter !== 'all' ? undefined : (
              <Button onClick={handleAddProduct} className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                Add Product
              </Button>
            )
          }
        />
      ) : (
        <ProductList
          products={filteredProducts}
          onEditProduct={handleEditProduct}
        />
      )}

      {/* Product Form Modal */}
      {showProductForm && (
        <ProductForm
          product={editingProduct}
          onSave={handleProductSave}
          onCancel={() => {
            setShowProductForm(false);
            setEditingProduct(null);
          }}
        />
      )}
    </div>
  );
};

export default InventoryManagement;
