/**
 * Mock API service for browser environments
 * Provides demo data and functionality when <PERSON><PERSON> backend is not available
 */

import { User, Product, Category, Supplier, Customer, ApiResponse } from '@/types';
import { LoginCredentials, LoginResponse } from './authService';
import { CreateCustomerRequest, UpdateCustomerRequest, CustomerWithStats } from './customerService';

// Mock data
const MOCK_USER: User = {
  id: 1,
  username: 'demo',
  email: '<EMAIL>',
  fullName: 'Demo User',
  role: 'admin',
  isActive: true,
  lastLogin: new Date(),
  createdAt: new Date(),
  updatedAt: new Date(),
};

const MOCK_CATEGORIES: Category[] = [
  {
    id: 1,
    name: 'Electronics',
    description: 'Electronic devices and accessories',
    parentId: undefined,
    sortOrder: 1,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 2,
    name: 'Home & Garden',
    description: 'Home and garden products',
    parentId: undefined,
    sortOrder: 2,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 3,
    name: 'Food & Beverages',
    description: 'Food and drink products',
    parentId: undefined,
    sortOrder: 3,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 4,
    name: 'Clothing',
    description: 'Clothing and fashion items',
    parentId: undefined,
    sortOrder: 4,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

const MOCK_SUPPLIERS: Supplier[] = [
  {
    id: 1,
    name: 'Tech Solutions Ltd',
    contactPerson: 'John Smith',
    email: '<EMAIL>',
    phone: '******-0101',
    address: '123 Tech Street, Silicon Valley, CA 94000',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 2,
    name: 'Home & Garden Supplies',
    contactPerson: 'Sarah Johnson',
    email: '<EMAIL>',
    phone: '******-0102',
    address: '456 Garden Ave, Green City, NY 10001',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 3,
    name: 'Fashion Forward Inc',
    contactPerson: 'Mike Chen',
    email: '<EMAIL>',
    phone: '******-0103',
    address: '789 Fashion Blvd, Style City, FL 33101',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 4,
    name: 'Food & Beverage Co',
    contactPerson: 'Lisa Rodriguez',
    email: '<EMAIL>',
    phone: '******-0104',
    address: '321 Food Court, Taste Town, TX 75001',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

const MOCK_CUSTOMERS: Customer[] = [
  {
    id: 1,
    name: 'John Doe',
    email: '<EMAIL>',
    phone: '******-1001',
    address: '123 Main Street, Anytown, ST 12345',
    dateOfBirth: new Date('1985-06-15'),
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 2,
    name: 'Jane Smith',
    email: '<EMAIL>',
    phone: '******-1002',
    address: '456 Oak Avenue, Somewhere, ST 67890',
    dateOfBirth: new Date('1990-03-22'),
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 3,
    name: 'Bob Johnson',
    email: '<EMAIL>',
    phone: '******-1003',
    address: '789 Pine Road, Elsewhere, ST 54321',
    dateOfBirth: new Date('1978-11-08'),
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 4,
    name: 'Alice Brown',
    email: '<EMAIL>',
    phone: '******-1004',
    address: '321 Elm Street, Nowhere, ST 98765',
    dateOfBirth: new Date('1992-09-14'),
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

const MOCK_PRODUCTS: Product[] = [
  {
    id: 1,
    sku: 'DEMO-001',
    barcode: '1234567890123',
    name: 'Demo Product 1',
    description: 'This is a demo product for browser testing',
    categoryId: 1,
    supplierId: 1,
    costPrice: 5000,
    sellingPrice: 8000,
    wholesalePrice: 7000,
    minSellingPrice: 6000,
    currentStock: 50,
    minStock: 10,
    maxStock: 100,
    unit: 'pcs',
    weight: 0.5,
    dimensions: '10x10x5 cm',
    imagePath: undefined,
    isActive: true,
    isTrackable: true,
    allowNegativeStock: false,
    isTaxable: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 2,
    sku: 'DEMO-002',
    barcode: '1234567890124',
    name: 'Demo Product 2',
    description: 'Another demo product for testing',
    categoryId: 1,
    supplierId: 1,
    costPrice: 3000,
    sellingPrice: 5000,
    wholesalePrice: 4500,
    minSellingPrice: 4000,
    currentStock: 25,
    minStock: 5,
    maxStock: 50,
    unit: 'pcs',
    weight: 0.3,
    dimensions: '8x8x3 cm',
    imagePath: undefined,
    isActive: true,
    isTrackable: true,
    allowNegativeStock: false,
    isTaxable: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 3,
    sku: 'DEMO-003',
    barcode: '1234567890125',
    name: 'Garden Tool Set',
    description: 'Complete garden tool set for home gardening',
    categoryId: 2,
    supplierId: 2,
    costPrice: 10000,
    sellingPrice: 15000,
    wholesalePrice: 13000,
    minSellingPrice: 12000,
    currentStock: 3,
    minStock: 10,
    maxStock: 30,
    unit: 'set',
    weight: 1.2,
    dimensions: '15x15x8 cm',
    imagePath: undefined,
    isActive: true,
    isTrackable: true,
    allowNegativeStock: false,
    isTaxable: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 4,
    sku: 'DEMO-004',
    barcode: '1234567890126',
    name: 'Organic Coffee Beans',
    description: 'Premium organic coffee beans - currently out of stock',
    categoryId: 3,
    supplierId: 4,
    costPrice: 7500,
    sellingPrice: 12000,
    wholesalePrice: 10000,
    minSellingPrice: 9000,
    currentStock: 0,
    minStock: 5,
    maxStock: 20,
    unit: 'kg',
    weight: 0.8,
    dimensions: '12x12x6 cm',
    imagePath: undefined,
    isActive: true,
    isTrackable: true,
    allowNegativeStock: false,
    isTaxable: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 5,
    sku: 'DEMO-005',
    barcode: '1234567890127',
    name: 'Cotton T-Shirt',
    description: 'Comfortable cotton t-shirt in various sizes',
    categoryId: 4,
    supplierId: 3,
    costPrice: 5000,
    sellingPrice: 9000,
    wholesalePrice: 7500,
    minSellingPrice: 6500,
    currentStock: 25,
    minStock: 10,
    maxStock: 50,
    unit: 'pcs',
    weight: 0.2,
    dimensions: '30x40x2 cm',
    imagePath: undefined,
    isActive: true,
    isTrackable: true,
    allowNegativeStock: false,
    isTaxable: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 6,
    sku: 'DEMO-006',
    barcode: '1234567890128',
    name: 'Wireless Headphones',
    description: 'High-quality wireless bluetooth headphones',
    categoryId: 1,
    supplierId: 1,
    costPrice: 15000,
    sellingPrice: 25000,
    wholesalePrice: 22000,
    minSellingPrice: 20000,
    currentStock: 15,
    minStock: 5,
    maxStock: 30,
    unit: 'pcs',
    weight: 0.3,
    dimensions: '20x18x8 cm',
    imagePath: undefined,
    isActive: true,
    isTrackable: true,
    allowNegativeStock: false,
    isTaxable: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

// Simulate API delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export class MockApiService {
  private static instance: MockApiService;
  private isAuthenticated = false;
  private currentUser: User | null = null;
  private token: string | null = null;
  private products: Product[] = [...MOCK_PRODUCTS]; // Initialize with mock products
  private categories: Category[] = [...MOCK_CATEGORIES]; // Initialize with mock categories
  private suppliers: Supplier[] = [...MOCK_SUPPLIERS]; // Initialize with mock suppliers
  private customers: Customer[] = [...MOCK_CUSTOMERS]; // Initialize with mock customers

  private constructor() {
    // Auto-authenticate for browser environment
    this.isAuthenticated = true;
    this.currentUser = MOCK_USER;
    this.token = 'mock-jwt-token-auto';
  }

  public static getInstance(): MockApiService {
    if (!MockApiService.instance) {
      MockApiService.instance = new MockApiService();
    }
    return MockApiService.instance;
  }

  // Authentication methods
  public async login(credentials: LoginCredentials): Promise<ApiResponse<LoginResponse>> {
    await delay(500); // Simulate network delay

    // Accept any credentials for demo purposes
    if (credentials.username && credentials.password) {
      this.isAuthenticated = true;
      this.currentUser = MOCK_USER;
      this.token = 'mock-jwt-token-' + Date.now();

      return {
        success: true,
        data: {
          user: MOCK_USER,
          token: this.token,
          refreshToken: 'mock-refresh-token-' + Date.now(),
        },
        error: undefined,
        message: 'Login successful (Demo Mode)',
      };
    }

    return {
      success: false,
      data: undefined,
      error: 'Invalid credentials',
      message: 'Login failed',
    };
  }

  public async logout(): Promise<ApiResponse<void>> {
    await delay(200);
    this.isAuthenticated = false;
    this.currentUser = null;
    this.token = null;

    return {
      success: true,
      data: undefined,
      error: undefined,
      message: 'Logged out successfully (Demo Mode)',
    };
  }

  public async validateToken(token: string): Promise<ApiResponse<boolean>> {
    await delay(100);
    return {
      success: true,
      data: this.isAuthenticated && this.token === token,
      error: undefined,
      message: 'Token validation complete',
    };
  }

  public async getCurrentUser(): Promise<ApiResponse<User>> {
    await delay(200);

    if (!this.isAuthenticated || !this.currentUser) {
      return {
        success: false,
        data: undefined,
        error: 'Not authenticated',
        message: 'User not authenticated',
      };
    }

    return {
      success: true,
      data: this.currentUser,
      error: undefined,
      message: 'User retrieved successfully',
    };
  }

  // Product methods
  public async getProducts(): Promise<ApiResponse<Product[]>> {
    await delay(300);

    if (!this.isAuthenticated) {
      return {
        success: false,
        data: undefined,
        error: 'Not authenticated',
        message: 'Authentication required',
      };
    }

    return {
      success: true,
      data: MOCK_PRODUCTS,
      error: undefined,
      message: 'Products retrieved successfully (Demo Mode)',
    };
  }

  public async getProductById(id: number): Promise<ApiResponse<Product>> {
    await delay(200);

    if (!this.isAuthenticated) {
      return {
        success: false,
        data: undefined,
        error: 'Not authenticated',
        message: 'Authentication required',
      };
    }

    const product = MOCK_PRODUCTS.find(p => p.id === id);
    if (!product) {
      return {
        success: false,
        data: undefined,
        error: 'Product not found',
        message: 'Product not found',
      };
    }

    return {
      success: true,
      data: product,
      error: undefined,
      message: 'Product retrieved successfully',
    };
  }

  public async getProductByBarcode(barcode: string): Promise<ApiResponse<Product>> {
    await delay(200);

    if (!this.isAuthenticated) {
      return {
        success: false,
        data: undefined,
        error: 'Not authenticated',
        message: 'Authentication required',
      };
    }

    const product = MOCK_PRODUCTS.find(p => p.barcode === barcode);
    if (!product) {
      return {
        success: false,
        data: undefined,
        error: 'Product not found',
        message: 'Product not found for barcode',
      };
    }

    return {
      success: true,
      data: product,
      error: undefined,
      message: 'Product retrieved successfully',
    };
  }

  // Helper method for simulating delay
  private async simulateDelay(): Promise<void> {
    await delay(300);
  }

  // Product Management
  public async createProduct(request: any): Promise<Product> {
    await this.simulateDelay();

    const newProduct: Product = {
      id: Date.now(), // Simple ID generation for demo
      sku: request.sku,
      barcode: request.barcode || undefined,
      name: request.name,
      description: request.description || undefined,
      categoryId: request.categoryId || undefined,
      supplierId: request.supplierId || undefined,
      costPrice: request.costPrice,
      sellingPrice: request.sellingPrice,
      wholesalePrice: request.wholesalePrice || undefined,
      minSellingPrice: request.minSellingPrice || undefined,
      currentStock: request.currentStock,
      minStock: request.minStock,
      maxStock: request.maxStock || undefined,
      unit: request.unit,
      weight: request.weight || undefined,
      dimensions: request.dimensions || undefined,
      imagePath: request.imagePath || undefined,
      isActive: true,
      isTrackable: request.isTrackable !== false,
      allowNegativeStock: request.allowNegativeStock || false,
      isTaxable: request.isTaxable !== false,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Add to mock products array
    this.products.push(newProduct);

    return newProduct;
  }

  public async updateProduct(productId: number, request: any): Promise<Product> {
    await this.simulateDelay();

    const productIndex = this.products.findIndex(p => p.id === productId);
    if (productIndex === -1) {
      throw new Error('Product not found');
    }

    const existingProduct = this.products[productIndex];
    const updatedProduct: Product = {
      ...existingProduct,
      ...request,
      id: productId, // Ensure ID doesn't change
      updatedAt: new Date().toISOString(),
    };

    this.products[productIndex] = updatedProduct;
    return updatedProduct;
  }

  // Transaction Management
  public async createTransaction(request: any, cashierId: number): Promise<any> {
    await this.simulateDelay();

    const transactionId = Date.now();
    const transactionNumber = `TXN-${Date.now()}`;

    // Calculate totals
    const subtotal = request.items.reduce((sum: number, item: any) => {
      return sum + (item.quantity * item.unit_price - (item.discount_amount || 0));
    }, 0);

    const discountAmount = request.discount_amount || 0;
    const discountPercentage = request.discount_percentage || 0;
    const discountFromPercentage = subtotal * (discountPercentage / 100);
    const totalDiscount = discountAmount + discountFromPercentage;

    const afterDiscount = subtotal - totalDiscount;
    const taxPercentage = request.tax_percentage || 0;
    const taxAmount = afterDiscount * (taxPercentage / 100);
    const totalAmount = afterDiscount + taxAmount;

    const totalPaid = request.payments.reduce((sum: number, payment: any) => sum + payment.amount, 0);
    const changeAmount = totalPaid - totalAmount;

    const transaction = {
      id: transactionId,
      transaction_number: transactionNumber,
      transaction_date: new Date().toISOString(),
      transaction_type: 'sale',
      customer_id: request.customer_id || undefined,
      customer_name: request.customer_name || undefined,
      subtotal,
      discount_amount: totalDiscount,
      discount_percentage: discountPercentage,
      tax_amount: taxAmount,
      tax_percentage: taxPercentage,
      total_amount: totalAmount,
      paid_amount: totalPaid,
      change_amount: changeAmount,
      status: 'completed',
      payment_status: totalPaid >= totalAmount ? 'paid' : 'partial',
      cashier_id: cashierId,
      notes: request.notes || undefined,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    const items = request.items.map((item: any, index: number) => ({
      id: transactionId + index + 1,
      transaction_id: transactionId,
      product_id: item.product_id,
      product_name: `Product ${item.product_id}`, // In real app, would lookup from products
      product_sku: `SKU-${item.product_id}`,
      quantity: item.quantity,
      unit_price: item.unit_price,
      total_price: item.quantity * item.unit_price - (item.discount_amount || 0),
      discount_amount: item.discount_amount || 0,
      created_at: new Date().toISOString(),
    }));

    const payments = request.payments.map((payment: any, index: number) => ({
      payment: {
        id: transactionId + index + 100,
        transaction_id: transactionId,
        payment_method_id: payment.payment_method_id,
        amount: payment.amount,
        reference_number: payment.reference_number || undefined,
        created_at: new Date().toISOString(),
      },
      payment_method: {
        id: payment.payment_method_id,
        name: 'Cash', // Mock payment method
        payment_type: 'cash',
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
    }));

    return {
      transaction,
      items,
      payments,
    };
  }

  // Category methods
  public async getCategories(): Promise<ApiResponse<Category[]>> {
    await delay(200);

    if (!this.isAuthenticated) {
      return {
        success: false,
        data: undefined,
        error: 'Not authenticated',
        message: 'Authentication required',
      };
    }

    return {
      success: true,
      data: this.categories,
      error: undefined,
      message: 'Categories retrieved successfully (Demo Mode)',
    };
  }

  public async getCategoryById(id: number): Promise<ApiResponse<Category>> {
    await delay(200);

    if (!this.isAuthenticated) {
      return {
        success: false,
        data: undefined,
        error: 'Not authenticated',
        message: 'Authentication required',
      };
    }

    const category = this.categories.find(c => c.id === id);
    if (!category) {
      return {
        success: false,
        data: undefined,
        error: 'Category not found',
        message: 'Category not found',
      };
    }

    return {
      success: true,
      data: category,
      error: undefined,
      message: 'Category retrieved successfully',
    };
  }

  public async createCategory(request: any): Promise<Category> {
    await this.simulateDelay();

    const newCategory: Category = {
      id: Date.now(), // Simple ID generation for demo
      name: request.name,
      description: request.description || undefined,
      parentId: request.parentId || undefined,
      sortOrder: request.sortOrder || this.categories.length + 1,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    this.categories.push(newCategory);
    return newCategory;
  }

  public async updateCategory(categoryId: number, request: any): Promise<Category> {
    await this.simulateDelay();

    const categoryIndex = this.categories.findIndex(c => c.id === categoryId);
    if (categoryIndex === -1) {
      throw new Error('Category not found');
    }

    const existingCategory = this.categories[categoryIndex];
    const updatedCategory: Category = {
      ...existingCategory,
      ...request,
      id: categoryId, // Ensure ID doesn't change
      updatedAt: new Date(),
    };

    this.categories[categoryIndex] = updatedCategory;
    return updatedCategory;
  }

  // Supplier methods
  public async getSuppliers(): Promise<ApiResponse<Supplier[]>> {
    await delay(200);

    if (!this.isAuthenticated) {
      return {
        success: false,
        data: undefined,
        error: 'Not authenticated',
        message: 'Authentication required',
      };
    }

    return {
      success: true,
      data: this.suppliers,
      error: undefined,
      message: 'Suppliers retrieved successfully (Demo Mode)',
    };
  }

  public async getSupplierById(id: number): Promise<ApiResponse<Supplier>> {
    await delay(200);

    if (!this.isAuthenticated) {
      return {
        success: false,
        data: undefined,
        error: 'Not authenticated',
        message: 'Authentication required',
      };
    }

    const supplier = this.suppliers.find(s => s.id === id);
    if (!supplier) {
      return {
        success: false,
        data: undefined,
        error: 'Supplier not found',
        message: 'Supplier not found',
      };
    }

    return {
      success: true,
      data: supplier,
      error: undefined,
      message: 'Supplier retrieved successfully',
    };
  }

  public async getSuppliersWithStats(): Promise<ApiResponse<any[]>> {
    await delay(200);

    if (!this.isAuthenticated) {
      return {
        success: false,
        data: undefined,
        error: 'Not authenticated',
        message: 'Authentication required',
      };
    }

    const suppliersWithStats = this.suppliers.map(supplier => {
      const productCount = this.products.filter(p => p.supplierId === supplier.id).length;
      return {
        ...supplier,
        productCount,
      };
    });

    return {
      success: true,
      data: suppliersWithStats,
      error: undefined,
      message: 'Suppliers with stats retrieved successfully (Demo Mode)',
    };
  }

  public async createSupplier(request: any): Promise<Supplier> {
    await this.simulateDelay();

    const newSupplier: Supplier = {
      id: Date.now(), // Simple ID generation for demo
      name: request.name,
      contactPerson: request.contactPerson || undefined,
      email: request.email || undefined,
      phone: request.phone || undefined,
      address: request.address || undefined,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    this.suppliers.push(newSupplier);
    return newSupplier;
  }

  public async updateSupplier(supplierId: number, request: any): Promise<Supplier> {
    await this.simulateDelay();

    const supplierIndex = this.suppliers.findIndex(s => s.id === supplierId);
    if (supplierIndex === -1) {
      throw new Error('Supplier not found');
    }

    const existingSupplier = this.suppliers[supplierIndex];
    const updatedSupplier: Supplier = {
      ...existingSupplier,
      ...request,
      id: supplierId, // Ensure ID doesn't change
      updatedAt: new Date(),
    };

    this.suppliers[supplierIndex] = updatedSupplier;
    return updatedSupplier;
  }

  // Customer methods
  public async getCustomers(): Promise<ApiResponse<Customer[]>> {
    await this.simulateDelay();
    return {
      success: true,
      data: this.customers,
      error: undefined,
      message: 'Customers retrieved successfully (Demo Mode)',
    };
  }

  public async getCustomerById(customerId: number): Promise<ApiResponse<Customer>> {
    await this.simulateDelay();

    const customer = this.customers.find(c => c.id === customerId);
    if (!customer) {
      return {
        success: false,
        data: undefined,
        error: 'Customer not found',
        message: 'Customer not found (Demo Mode)',
      };
    }

    return {
      success: true,
      data: customer,
      error: undefined,
      message: 'Customer retrieved successfully (Demo Mode)',
    };
  }

  public async getCustomersWithStats(): Promise<ApiResponse<CustomerWithStats[]>> {
    await this.simulateDelay();

    const customersWithStats = this.customers.map(customer => ({
      ...customer,
      totalTransactions: Math.floor(Math.random() * 20) + 1,
      totalSpent: Math.floor(Math.random() * 500000) + 50000,
      lastTransactionDate: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000),
    }));

    return {
      success: true,
      data: customersWithStats,
      error: undefined,
      message: 'Customers with stats retrieved successfully (Demo Mode)',
    };
  }

  public async createCustomer(request: CreateCustomerRequest): Promise<Customer> {
    await this.simulateDelay();

    // Check for duplicate phone number
    if (request.phone && this.customers.some(c => c.phone === request.phone)) {
      throw new Error('A customer with this phone number already exists');
    }

    const newCustomer: Customer = {
      id: Date.now(), // Simple ID generation for demo
      name: request.name,
      email: request.email || undefined,
      phone: request.phone || undefined,
      address: request.address || undefined,
      dateOfBirth: request.dateOfBirth || undefined,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    this.customers.push(newCustomer);
    return newCustomer;
  }

  public async updateCustomer(customerId: number, request: UpdateCustomerRequest): Promise<Customer> {
    await this.simulateDelay();

    const customerIndex = this.customers.findIndex(c => c.id === customerId);
    if (customerIndex === -1) {
      throw new Error('Customer not found');
    }

    // Check for duplicate phone number (excluding current customer)
    if (request.phone && this.customers.some(c => c.id !== customerId && c.phone === request.phone)) {
      throw new Error('A customer with this phone number already exists');
    }

    const existingCustomer = this.customers[customerIndex];
    const updatedCustomer: Customer = {
      ...existingCustomer,
      ...request,
      id: customerId, // Ensure ID doesn't change
      updatedAt: new Date(),
    };

    this.customers[customerIndex] = updatedCustomer;
    return updatedCustomer;
  }

  public async searchCustomersByPhone(phone: string): Promise<ApiResponse<Customer[]>> {
    await this.simulateDelay();

    const matchingCustomers = this.customers.filter(c =>
      c.phone && c.phone.toLowerCase().includes(phone.toLowerCase())
    ).slice(0, 10);

    return {
      success: true,
      data: matchingCustomers,
      error: undefined,
      message: 'Customers search completed (Demo Mode)',
    };
  }

  public async searchCustomersByName(name: string): Promise<ApiResponse<Customer[]>> {
    await this.simulateDelay();

    const matchingCustomers = this.customers.filter(c =>
      c.name.toLowerCase().includes(name.toLowerCase())
    ).slice(0, 10);

    return {
      success: true,
      data: matchingCustomers,
      error: undefined,
      message: 'Customers search completed (Demo Mode)',
    };
  }

  // Getters
  public getToken(): string | null {
    return this.token;
  }

  public getIsAuthenticated(): boolean {
    return this.isAuthenticated;
  }

  public setToken(token: string | null): void {
    this.token = token;
    this.isAuthenticated = !!token;
  }
}

export const mockApiService = MockApiService.getInstance();
