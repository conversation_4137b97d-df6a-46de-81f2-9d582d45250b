use sqlx::{Pool, Sqlite};
use anyhow::Result;
use bcrypt;

pub async fn run_migrations(pool: &Pool<Sqlite>) -> Result<()> {
    // Disable foreign key constraints during migration
    sqlx::query("PRAGMA foreign_keys = OFF")
        .execute(pool)
        .await?;

    // Create users table
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL UNIQUE,
            email TEXT UNIQUE,
            password_hash TEXT NOT NULL,
            full_name TEXT NOT NULL,
            role TEXT NOT NULL CHECK (role IN ('admin', 'manager', 'cashier')),
            is_active BOOLEAN NOT NULL DEFAULT 1,
            last_login DATETIME,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
        )
        "#,
    )
    .execute(pool)
    .await?;

    // Create categories table
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            description TEXT,
            parent_id INTEGER,
            sort_order INTEGER NOT NULL DEFAULT 0,
            is_active BOOLEAN NOT NULL DEFAULT 1,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL
        )
        "#,
    )
    .execute(pool)
    .await?;

    // Create suppliers table
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS suppliers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            contact_person TEXT,
            email TEXT,
            phone TEXT,
            address TEXT,
            is_active BOOLEAN NOT NULL DEFAULT 1,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
        )
        "#,
    )
    .execute(pool)
    .await?;

    // Create products table
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            sku TEXT NOT NULL UNIQUE,
            barcode TEXT UNIQUE,
            name TEXT NOT NULL,
            description TEXT,
            category_id INTEGER,
            supplier_id INTEGER,
            cost_price REAL NOT NULL,
            selling_price REAL NOT NULL,
            wholesale_price REAL,
            min_selling_price REAL,
            current_stock INTEGER NOT NULL DEFAULT 0,
            min_stock INTEGER NOT NULL DEFAULT 0,
            max_stock INTEGER,
            unit TEXT NOT NULL DEFAULT 'pcs',
            weight REAL,
            dimensions TEXT,
            image_path TEXT,
            is_active BOOLEAN NOT NULL DEFAULT 1,
            is_trackable BOOLEAN NOT NULL DEFAULT 1,
            allow_negative_stock BOOLEAN NOT NULL DEFAULT 0,
            is_taxable BOOLEAN NOT NULL DEFAULT 1,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
            FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE SET NULL
        )
        "#,
    )
    .execute(pool)
    .await?;

    // Create customers table
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS customers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            email TEXT,
            phone TEXT,
            address TEXT,
            date_of_birth DATE,
            is_active BOOLEAN NOT NULL DEFAULT 1,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
        )
        "#,
    )
    .execute(pool)
    .await?;

    // Create transactions table
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS transactions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            transaction_number TEXT NOT NULL UNIQUE,
            transaction_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            transaction_type TEXT NOT NULL CHECK (transaction_type IN ('sale', 'return', 'void')),
            customer_id INTEGER,
            customer_name TEXT,
            subtotal REAL NOT NULL,
            discount_amount REAL NOT NULL DEFAULT 0,
            discount_percentage REAL NOT NULL DEFAULT 0,
            tax_amount REAL NOT NULL DEFAULT 0,
            tax_percentage REAL NOT NULL DEFAULT 0,
            total_amount REAL NOT NULL,
            paid_amount REAL NOT NULL DEFAULT 0,
            change_amount REAL NOT NULL DEFAULT 0,
            status TEXT NOT NULL CHECK (status IN ('pending', 'completed', 'cancelled', 'refunded')),
            payment_status TEXT NOT NULL CHECK (payment_status IN ('unpaid', 'partial', 'paid', 'overpaid')),
            cashier_id INTEGER NOT NULL,
            notes TEXT,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
            FOREIGN KEY (cashier_id) REFERENCES users(id) ON DELETE RESTRICT
        )
        "#,
    )
    .execute(pool)
    .await?;

    // Create transaction_items table
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS transaction_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            transaction_id INTEGER NOT NULL,
            product_id INTEGER NOT NULL,
            product_name TEXT NOT NULL,
            product_sku TEXT NOT NULL,
            quantity REAL NOT NULL,
            unit_price REAL NOT NULL,
            total_price REAL NOT NULL,
            discount_amount REAL NOT NULL DEFAULT 0,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE CASCADE,
            FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE RESTRICT
        )
        "#,
    )
    .execute(pool)
    .await?;

    // Create payment_methods table
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS payment_methods (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            type TEXT NOT NULL CHECK (type IN ('cash', 'card', 'digital')),
            is_active BOOLEAN NOT NULL DEFAULT 1,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
        )
        "#,
    )
    .execute(pool)
    .await?;

    // Create transaction_payments table
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS transaction_payments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            transaction_id INTEGER NOT NULL,
            payment_method_id INTEGER NOT NULL,
            amount REAL NOT NULL,
            reference_number TEXT,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE CASCADE,
            FOREIGN KEY (payment_method_id) REFERENCES payment_methods(id) ON DELETE RESTRICT
        )
        "#,
    )
    .execute(pool)
    .await?;

    // Create stock_movements table for inventory tracking
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS stock_movements (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            product_id INTEGER NOT NULL,
            movement_type TEXT NOT NULL CHECK (movement_type IN ('in', 'out', 'adjustment')),
            quantity REAL NOT NULL,
            reference_type TEXT, -- 'transaction', 'adjustment', 'purchase', etc.
            reference_id INTEGER,
            notes TEXT,
            created_by INTEGER NOT NULL,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
            FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT
        )
        "#,
    )
    .execute(pool)
    .await?;

    // Insert default payment methods
    sqlx::query(
        r#"
        INSERT OR IGNORE INTO payment_methods (id, name, type) VALUES 
        (1, 'Cash', 'cash'),
        (2, 'Credit Card', 'card'),
        (3, 'Debit Card', 'card'),
        (4, 'Digital Wallet', 'digital')
        "#,
    )
    .execute(pool)
    .await?;

    // Insert default admin user (password: admin123)
    // First delete any existing admin user to ensure we have the correct hash
    sqlx::query("DELETE FROM users WHERE username = 'admin'")
        .execute(pool)
        .await?;

    // Generate a fresh bcrypt hash for admin123
    let password_hash = bcrypt::hash("admin123", bcrypt::DEFAULT_COST)
        .map_err(|e| anyhow::anyhow!("Failed to hash password: {}", e))?;

    sqlx::query(
        r#"
        INSERT INTO users (id, username, email, password_hash, full_name, role) VALUES
        (1, 'admin', '<EMAIL>', ?, 'Administrator', 'admin')
        "#,
    )
    .bind(&password_hash)
    .execute(pool)
    .await?;

    // Insert sample categories
    sqlx::query(
        r#"
        INSERT OR IGNORE INTO categories (id, name, description, sort_order) VALUES
        (1, 'Beverages', 'Drinks and beverages', 1),
        (2, 'Food', 'Food items', 2),
        (3, 'Snacks', 'Snacks and confectionery', 3)
        "#,
    )
    .execute(pool)
    .await?;

    // Insert sample products
    sqlx::query(
        r#"
        INSERT OR IGNORE INTO products (id, sku, barcode, name, description, category_id, cost_price, selling_price, current_stock, min_stock, unit) VALUES
        (1, 'CC-330', '1234567890123', 'Coca Cola 330ml', 'Refreshing cola drink', 1, 3000, 5000, 50, 10, 'pcs'),
        (2, 'IM-GOR', '1234567890124', 'Indomie Goreng', 'Instant fried noodles', 2, 2500, 4000, 100, 20, 'pcs'),
        (3, 'AQ-600', '1234567890125', 'Aqua 600ml', 'Mineral water', 1, 2000, 3500, 75, 15, 'pcs'),
        (4, 'BR-WH', '1234567890126', 'Bread White', 'Fresh white bread', 2, 8000, 12000, 25, 5, 'pcs'),
        (5, 'MK-VAN', '1234567890127', 'Milk Vanilla 250ml', 'Vanilla flavored milk', 1, 4000, 6500, 40, 10, 'pcs')
        "#,
    )
    .execute(pool)
    .await?;

    // Create indexes for better performance
    sqlx::query("CREATE INDEX IF NOT EXISTS idx_products_sku ON products(sku)")
        .execute(pool)
        .await?;
    
    sqlx::query("CREATE INDEX IF NOT EXISTS idx_products_barcode ON products(barcode)")
        .execute(pool)
        .await?;
    
    sqlx::query("CREATE INDEX IF NOT EXISTS idx_transactions_number ON transactions(transaction_number)")
        .execute(pool)
        .await?;
    
    sqlx::query("CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions(transaction_date)")
        .execute(pool)
        .await?;
    
    sqlx::query("CREATE INDEX IF NOT EXISTS idx_transaction_items_transaction ON transaction_items(transaction_id)")
        .execute(pool)
        .await?;

    // Re-enable foreign key constraints
    sqlx::query("PRAGMA foreign_keys = ON")
        .execute(pool)
        .await?;

    Ok(())
}
