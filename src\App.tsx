import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from 'sonner';
import { useEffect } from 'react';

// Import components
import ErrorBoundary from '@/components/common/ErrorBoundary';
import Layout from '@/components/common/Layout';
import LoginPage from '@/components/auth/LoginPage';
import Dashboard from '@/components/dashboard/Dashboard';
import POS from '@/components/pos/POS';
import InventoryManagement from '@/components/inventory/InventoryManagement';
import TransactionHistory from '@/components/transactions/TransactionHistory';
import TestPage from '@/components/testing/TestPage';
import CategoryManagement from '@/components/management/CategoryManagement';
import SupplierManagement from '@/components/management/SupplierManagement';
import CustomerManagement from '@/components/management/CustomerManagement';
import ProtectedRoute from '@/components/common/ProtectedRoute';
import { logEnvironmentInfo, getCurrentEnvironment, waitForTauri } from '@/utils/environment';
import { checkEnvironment } from '@/utils/environmentCheck';
import { useAuthStore } from '@/stores/authStore';

const queryClient = new QueryClient();

function App() {
  const initializeAuth = useAuthStore((state) => state.initializeAuth);

  useEffect(() => {
    const initializeApp = async () => {
      // Log environment information on app startup
      logEnvironmentInfo();

      // Check Tauri availability
      checkEnvironment();

      const environment = getCurrentEnvironment();
      if (environment === 'browser') {
        console.warn('⚠️ Running in browser mode - using mock data for testing');
        console.log('📝 To use real database, run: npm run tauri dev');
      } else {
        console.log('✅ Running in Tauri environment - using real database');

        // Wait for Tauri to be fully available
        const tauriReady = await waitForTauri(3000);
        if (!tauriReady) {
          console.warn('⚠️ Tauri took longer than expected to initialize');
        }
      }

      // Initialize authentication state
      initializeAuth();
    };

    initializeApp();
  }, [initializeAuth]);

  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <Router>
          <div className="min-h-screen bg-gray-50">
            <Routes>
              <Route path="/login" element={<LoginPage />} />
              <Route path="/" element={
                <ProtectedRoute>
                  <Layout />
                </ProtectedRoute>
              }>
                <Route index element={<Dashboard />} />
                <Route path="pos" element={<POS />} />
                <Route path="inventory" element={<InventoryManagement />} />
                <Route path="transactions" element={<TransactionHistory />} />
                <Route path="categories" element={<CategoryManagement />} />
                <Route path="suppliers" element={<SupplierManagement />} />
                <Route path="customers" element={<CustomerManagement />} />
                <Route path="test" element={<TestPage />} />
                {/* More routes will be added later */}
              </Route>
            </Routes>
            <Toaster position="top-right" />
          </div>
        </Router>
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

export default App;